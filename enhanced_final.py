#!/usr/bin/env python3

import os
import json
import re
import zipfile
import requests
import tabula
import pandas as pd
from datetime import datetime
from PyPDF2 import PdfReader

# Set JAVA_HOME for tabula-py
os.environ['JAVA_HOME'] = os.path.join(os.getcwd(), 'jdk-11.0.2')
os.environ['PATH'] = os.path.join(os.getcwd(), 'jdk-11.0.2', 'bin') + os.pathsep + os.environ.get('PATH', '')

DEFAULT_API_URL = "https://bonum.in/boatsai/insert_invoice_data.php"  # API endpoint for invoice data
DEFAULT_API_KEY = "your-api-key"  # Replace with actual API key
DEFAULT_API_TIMEOUT = 30  # Timeout in seconds

def extract_product_title_with_tabula(pdf_path, page_num):
    """Extract product title using tabula-py for better table structure recognition"""
    print(f"   🔍 Using tabula-py to extract product title from page {page_num}...")

    try:
        # Try different extraction methods
        all_tables = []

        # Method 1: Lattice extraction
        try:
            tables = tabula.read_pdf(pdf_path, pages=page_num, multiple_tables=True, lattice=True)
            all_tables.extend(tables)
        except:
            pass

        # Method 2: Stream extraction
        try:
            tables = tabula.read_pdf(pdf_path, pages=page_num, multiple_tables=True, stream=True)
            all_tables.extend(tables)
        except:
            pass

        # Method 3: Guess extraction
        try:
            tables = tabula.read_pdf(pdf_path, pages=page_num, multiple_tables=True, guess=True)
            all_tables.extend(tables)
        except:
            pass

        # Method 4: Extract entire page
        try:
            entire_page = tabula.read_pdf(pdf_path, pages=page_num, pandas_options={'header': None})
            if entire_page:
                all_tables.extend(entire_page)
        except:
            pass

        # Process extracted tables to find product titles
        for df in all_tables:
            if df.empty:
                continue

            # Look for product titles in table data
            product_title = extract_product_from_dataframe(df)
            if product_title:
                print(f"   ✅ Found product title with tabula-py: {product_title}")
                return product_title

        print("   ⚠️ No product title found with tabula-py")
        return None

    except Exception as e:
        print(f"   ❌ Error using tabula-py: {str(e)}")
        return None

def extract_product_with_tabula(pdf_path, page_num):
    """Extract product name from 'Product' column using tabula-py"""
    print(f"   🔍 Using tabula-py to extract product from 'Product' column on page {page_num}...")

    try:
        # Try different extraction methods
        all_tables = []

        # Method 1: Lattice extraction
        try:
            tables = tabula.read_pdf(pdf_path, pages=page_num, multiple_tables=True, lattice=True)
            all_tables.extend(tables)
        except:
            pass

        # Method 2: Stream extraction
        try:
            tables = tabula.read_pdf(pdf_path, pages=page_num, multiple_tables=True, stream=True)
            all_tables.extend(tables)
        except:
            pass

        # Method 3: Guess extraction
        try:
            tables = tabula.read_pdf(pdf_path, pages=page_num, multiple_tables=True, guess=True)
            all_tables.extend(tables)
        except:
            pass

        # Method 4: Extract entire page
        try:
            entire_page = tabula.read_pdf(pdf_path, pages=page_num, pandas_options={'header': None})
            if entire_page:
                all_tables.extend(entire_page)
        except:
            pass

        # Process extracted tables to find product from 'Product' column
        for df in all_tables:
            if df.empty:
                continue

            # Look for product in 'Product' column specifically
            product = extract_product_from_product_column(df)
            if product:
                print(f"   ✅ Found product from 'Product' column with tabula-py: {product}")
                return product

        print("   ⚠️ No product found in 'Product' column with tabula-py")
        return None

    except Exception as e:
        print(f"   ❌ Error using tabula-py for product extraction: {str(e)}")
        return None

def similar_strings(a, b):
    """Calculate similarity between two strings using a simple approach"""
    if not a or not b:
        return 0.0
    a, b = a.lower(), b.lower()
    if a == b:
        return 1.0
    set_a, set_b = set(a), set(b)
    intersection = len(set_a & set_b)
    union = len(set_a | set_b)
    if union == 0:
        return 0.0
    return intersection / union

def extract_product_from_dataframe(df):
    """Extract product title from a pandas DataFrame"""
    try:
        # First check if we have a "Title" header - special handling for bold text across multiple lines
        title_column_result = extract_product_from_title_column(df)
        if title_column_result:
            return title_column_result

        # Convert all data to string and combine
        all_text = ""
        for col in df.columns:
            for val in df[col].dropna():
                if pd.notna(val):
                    all_text += str(val) + " "

        # Look for product patterns in the combined text
        product_patterns = [
            # Product name followed by HSN/SAC
            r'([A-Za-z][A-Za-z0-9\s\-\(\)\.\/]{10,}?)\s+(?:HSN|SAC)[\s:]*\d+',
            # Product name in table row with quantities
            r'([A-Za-z][A-Za-z0-9\s\-\(\)\.\/]{10,}?)\s+\d+\s+[\d.]+',
            # Product name followed by IGST percentage
            r'([A-Za-z][A-Za-z0-9\s\-\(\)\.\/]{10,}?)\s+IGST[\s:]*\d+',
            # General product pattern
            r'([A-Za-z][A-Za-z0-9\s\-\(\)\.\/]{15,}?)(?=\s+(?:\d+|HSN|SAC|IGST))',
        ]

        for pattern in product_patterns:
            match = re.search(pattern, all_text, re.IGNORECASE)
            if match:
                candidate = match.group(1).strip()
                cleaned = clean_product_title(candidate)
                if is_valid_product_title(cleaned) and len(cleaned) > 10:
                    return cleaned

        # Look for product titles in individual cells
        for col in df.columns:
            for val in df[col].dropna():
                if pd.notna(val):
                    val_str = str(val).strip()
                    if len(val_str) > 15 and is_valid_product_title(val_str):
                        cleaned = clean_product_title(val_str)
                        if cleaned and len(cleaned) > 10:
                            return cleaned

        return None

    except Exception as e:
        print(f"   ❌ Error processing DataFrame: {str(e)}")
        return None

def extract_product_from_title_column(df):
    """Extract product title specifically when 'Title' or 'Product name' header is found - handles bold text across multiple lines"""
    try:
        # Look for columns that contain "Title" or "Product name" (case insensitive)
        title_columns = []
        for col in df.columns:
            if pd.notna(col):
                col_lower = str(col).lower().strip()
                if 'title' in col_lower or 'product name' in col_lower or 'product' in col_lower:
                    title_columns.append(col)

        if not title_columns:
            return None

        print(f"   🎯 Found product column(s): {title_columns}")

        # Process each title column
        for title_col in title_columns:
            # Get all non-null values from this column
            title_values = []
            for val in df[title_col].dropna():
                if pd.notna(val):
                    val_str = str(val).strip()
                    if val_str and val_str.lower() not in ['title', 'product name', 'product']:  # Skip the header itself
                        title_values.append(val_str)

            if not title_values:
                continue

            # Combine up to 4 lines of title values to form complete product name
            combined_title_lines = []
            for i, val in enumerate(title_values[:4]):  # Limit to max 4 lines
                # Check if this looks like part of a product name (not metadata)
                if is_likely_product_text(val):
                    combined_title_lines.append(val)
                else:
                    # Stop if we hit non-product text (like HSN, quantities, etc.)
                    break

            if combined_title_lines:
                # Join the lines with space to form complete product title
                combined_title = ' '.join(combined_title_lines)
                cleaned_title = clean_product_title(combined_title)

                if is_valid_product_title(cleaned_title) and len(cleaned_title) > 10:
                    print(f"   ✅ Extracted multi-line product title from 'Title' column: {cleaned_title}")
                    return cleaned_title

        return None

    except Exception as e:
        print(f"   ❌ Error processing Title column: {str(e)}")
        return None

def extract_product_from_product_column(df):
    """Extract product name specifically from 'Product' column - handles multi-line product names"""
    try:
        # Look for columns that contain "Product" (case insensitive) but NOT "Title" or "Description"
        product_columns = []
        for col in df.columns:
            if pd.notna(col):
                col_lower = str(col).lower().strip()
                # Only match "Product" column, not "Product Title", "Product Description", etc.
                if (col_lower == 'product' or
                    (col_lower.startswith('product') and
                     'title' not in col_lower and
                     'description' not in col_lower and
                     'name' not in col_lower)):
                    product_columns.append(col)

        if not product_columns:
            return None

        print(f"   🎯 Found 'Product' column(s): {product_columns}")

        # Process each product column
        for product_col in product_columns:
            # Get all non-null values from this column
            product_values = []
            for val in df[product_col].dropna():
                if pd.notna(val):
                    val_str = str(val).strip()
                    if val_str and val_str.lower() not in ['product']:  # Skip the header itself
                        product_values.append(val_str)

            if not product_values:
                continue

            # Combine up to 4 lines of product values to form complete product name
            combined_product_lines = []
            for i, val in enumerate(product_values[:4]):  # Limit to max 4 lines
                # Check if this looks like part of a product name (not metadata)
                if is_likely_product_text(val):
                    combined_product_lines.append(val)
                else:
                    # Stop if we hit non-product text (like HSN, quantities, etc.)
                    break

            if combined_product_lines:
                # Join the lines with space to form complete product name
                combined_product = ' '.join(combined_product_lines)
                cleaned_product = clean_product_title(combined_product)

                if is_valid_product_title(cleaned_product) and len(cleaned_product) > 10:
                    print(f"   ✅ Extracted multi-line product from 'Product' column: {cleaned_product}")
                    return cleaned_product

        return None

    except Exception as e:
        print(f"   ❌ Error processing Product column: {str(e)}")
        return None

# def extract_product_from_title_column(df):
#     """
#     Unified product name extractor for both 'Title' and 'Product' formats.
#     Joins multi-line values and removes unwanted metadata like FSN, IMEI, etc.
#     """
#     title_columns = [col for col in df.columns if "title" in col.lower() or "product" in col.lower()]
#     if not title_columns:
#         return ""

#     title_col = title_columns[0]

#     # Join all non-empty strings under the title/product column
#     title_values = df[title_col].dropna().astype(str).tolist()

#     # Join up to 5 consecutive lines, trimming whitespace and line breaks
#     combined_lines = []
#     for val in title_values:
#         # Skip lines that are clearly codes or metadata
#         if re.search(r"(IMEI|FSN|SKU|Serial|SrNo|Item Code)", val, re.IGNORECASE):
#             continue
#         combined_lines.append(val.strip())

#     # Join them with space
#     combined_title = " ".join(combined_lines)

#     # Clean out FSN/IMEI/suffix garbage
#     cleaned_title = clean_product_title(combined_title)

#     if is_valid_product_title(cleaned_title) and len(cleaned_title) > 10:
#         print(f"   ✅ Multi-line product title extracted: {cleaned_title}")
#         return cleaned_title

#     return ""

def is_likely_product_text(text):
    """Check if text is likely part of a product name (not metadata like HSN, quantities, etc.)"""
    if not text or len(text.strip()) < 3:
        return False

    text_lower = text.lower().strip()

    # Skip obvious non-product patterns
    non_product_patterns = [
        r'^hsn[:\s]*\d+',
        r'^sac[:\s]*\d+',
        r'^\d+\s*$',  # Just numbers
        r'^\d+\.\d+\s*$',  # Decimal numbers
        r'^qty[:\s]*\d+',
        r'^quantity[:\s]*\d+',
        r'^\d+\s*(pcs|units|qty|piece|pieces)\s*$',
        r'^(igst|cgst|sgst)[:\s]*\d+',
        r'^\d+\s*%\s*$',  # Percentage
        r'^rs\.?\s*\d+',
        r'^₹\s*\d+',
        r'^\d{2}-\d{2}-\d{4}$',  # Date
        r'^[a-z0-9]{10,}$',  # Long alphanumeric codes
    ]

    for pattern in non_product_patterns:
        if re.match(pattern, text_lower):
            return False

    # Must contain at least some alphabetic characters
    if not re.search(r'[a-zA-Z]', text):
        return False

    # Skip if it's mostly numbers
    alpha_count = len(re.findall(r'[a-zA-Z]', text))
    total_count = len(re.sub(r'\s', '', text))
    if total_count > 0 and alpha_count / total_count < 0.3:
        return False

    return True

def clean_product_title(title):
    """Clean and format product title"""
    if not title:
        return ""

    title = title.strip()

    # Remove common suffixes and patterns
    title = re.sub(r'\s*\d+\s*(?:Rs\.?|₹)?\s*\d*\.?\d*$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\s*\(Pack of \d+\)$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\bFSN\b.*', '', title, flags=re.IGNORECASE).strip()
    title = re.sub(r'\s+', ' ', title.strip())
    title = re.sub(r'\s*\b\d{4,}\b$', '', title)  # Remove trailing large numbers
    title = re.sub(r'\s*(HSN|SAC|SKU|FSN|Model|Code):?\s*[A-Za-z0-9-]+$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\s*(IGST|CGST|SGST)[\s:]*\d+\.?\d*\s*%?$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\s+', ' ', title).strip()
    title = re.sub(r'[^a-zA-Z\s]$', '', title)

    return title

def is_valid_product_title(title):
    """Check if the extracted text is a valid product title"""
    if not title:
        return False
    if len(title) < 3:
        return False
    if not re.search(r'[a-zA-Z]', title):
        return False

    # Skip obvious non-product patterns
    invalid_patterns = [
        r'^(Rs\.?|₹)?\s*\d+\.?\d*$',
        r'^\d+\s*(pcs|units|qty|piece|pieces)?$',
        r'^(HSN|SAC|SKU|FSN|IMEI|PAN|GSTIN|CIN|IRN)[:\s]*[A-Za-z0-9]*$',
        r'^(Contact|Phone|Email|Address|Website).*$',
        r'^(Total|Grand|Sub|Net|Final)\s*(Amount|Price|Cost|Value).*$',
        r'^(Tax|GST|IGST|SGST|CGST|VAT).*$',
        r'^(Qty|Quantity|Gross|Discount|Taxable).*$',
        r'^(Invoice|Bill|Receipt|Order).*$',
        r'^(Date|Time|Number|No\.?).*$',
        r'^[A-Z]{2,}\d+[A-Z]*$',
        r'^\d{2}-\d{2}-\d{4}$',
        r'^[A-Z0-9]{10,}$',
    ]

    for pattern in invalid_patterns:
        if re.match(pattern, title, re.IGNORECASE):
            return False

    # Check for non-product words
    non_product_words = {
        'contact', 'flipkart', 'amazon', 'seller', 'buyer', 'customer', 'company',
        'address', 'phone', 'email', 'website', 'total', 'amount', 'price', 'cost',
        'tax', 'gst', 'igst', 'sgst', 'cgst', 'hsn', 'sac', 'qty', 'quantity',
        'gross', 'discount', 'taxable', 'invoice', 'bill', 'receipt', 'order',
        'date', 'time', 'number', 'signature', 'authorized', 'signatory', 'imei', 'serial'
    }

    words = title.lower().split()
    if len(words) > 0:
        non_product_count = sum(1 for word in words if word in non_product_words)
        if non_product_count / len(words) > 0.7:
            return False

    # Skip table headers
    table_headers = [
        'title', 'qty', 'gross', 'discount', 'taxable', 'igst', 'sgst', 'total',
        'amount', 'price', 'rate', 'value', 'description', 'item', 'product'
    ]

    if title.lower().strip() in table_headers:
        return False

    if len(title) > 100:
        return False

    return True

def extract_product_title_enhanced(text, pdf_path, page_num):
    """Enhanced product title extraction using both PyPDF2 and tabula-py"""
    print(f"   🔍 Enhanced product title extraction for page {page_num}...")

    # First try tabula-py for better table structure recognition
    tabula_result = extract_product_title_with_tabula(pdf_path, page_num)
    if tabula_result:
        return tabula_result

    # Fallback to original text-based extraction
    print("   📝 Falling back to text-based extraction...")
    return extract_product_title_original(text)

def extract_product_title_original(text):
    """Original product title extraction logic from your code"""
    if not text:
        return ""

    # Try vertical scanning first
    lines = text.split('\n')
    product_headers = [
        'description', 'product', 'title', 'item', 'name', 'goods',
        'product title', 'product name', 'item description', 'description of goods',
        'product description', 'item name', 'commodity', 'article', 'details'
    ]

    for i, line in enumerate(lines):
        line_lower = line.lower().strip()
        for header in product_headers:
            if (header in line_lower and len(line_lower) < 100):
                # Special handling for "Title" and "Product name" headers - extract bold text across multiple lines
                if ((header == 'title' and ('title' in line_lower and len(line_lower.strip()) <= 10)) or
                    (header == 'product name' and ('product name' in line_lower and len(line_lower.strip()) <= 15))):
                    title_result = extract_multiline_title_from_text(lines, i)
                    if title_result:
                        return title_result

                # Look for product title in next few lines (original logic)
                for j in range(i + 1, min(i + 10, len(lines))):
                    if j >= len(lines):
                        break

                    candidate_line = lines[j].strip()
                    if not candidate_line:
                        continue

                    # Skip unwanted patterns
                    skip_words = ['hsn', 'sac', 'qty', 'gross', 'discount', 'taxable', 'igst', 'total']
                    if any(skip_word in candidate_line.lower() for skip_word in skip_words):
                        continue

                    if (re.search(r'[A-Za-z]', candidate_line) and
                        len(candidate_line) > 10 and
                        not re.search(r'^\d+\s*%', candidate_line)):

                        cleaned_candidate = clean_product_title(candidate_line)
                        if is_valid_product_title(cleaned_candidate) and len(cleaned_candidate) > 5:
                            return cleaned_candidate

    # Try table pattern matching
    for i, line in enumerate(lines):
        line_stripped = line.strip()
        if re.search(r'^[A-Za-z][A-Za-z0-9\s\-\(\)\.]{10,}?\s+\d+\s+[\d.]+', line_stripped):
            product_match = re.match(r'^([A-Za-z][A-Za-z0-9\s\-\(\)\.]{10,}?)\s+\d+\s+[\d.]+', line_stripped)
            if product_match:
                product_candidate = product_match.group(1).strip()
                cleaned_candidate = clean_product_title(product_candidate)
                if is_valid_product_title(cleaned_candidate) and len(cleaned_candidate) > 5:
                    return cleaned_candidate

    return ""

def extract_multiline_title_from_text(lines, title_header_index):
    """Extract product title from multiple lines when 'Title' or 'Product name' header is found"""
    try:
        header_text = lines[title_header_index].strip() if title_header_index < len(lines) else "Product header"
        print(f"   🎯 Found '{header_text}' header at line {title_header_index}, extracting multi-line product name...")

        # Look at the next few lines after the "Title" header
        combined_title_lines = []
        for j in range(title_header_index + 1, min(title_header_index + 5, len(lines))):  # Check up to 4 lines
            if j >= len(lines):
                break

            candidate_line = lines[j].strip()
            if not candidate_line:
                continue

            # Check if this line looks like part of a product name
            if is_likely_product_text(candidate_line):
                combined_title_lines.append(candidate_line)
            else:
                # Stop if we hit non-product text (like HSN, quantities, etc.)
                break

        if combined_title_lines:
            # Join the lines with space to form complete product title
            combined_title = ' '.join(combined_title_lines)
            cleaned_title = clean_product_title(combined_title)

            if is_valid_product_title(cleaned_title) and len(cleaned_title) > 10:
                print(f"   ✅ Extracted multi-line product title from text: {cleaned_title}")
                return cleaned_title

        return None

    except Exception as e:
        print(f"   ❌ Error extracting multi-line title from text: {str(e)}")
        return None

def extract_zip_files(folder_path):
    """Extract all zip files in the specified folder"""
    extracted_files = []
    print("\n" + "-"*80)
    print("📋 EXTRACT_ZIP_FILES FUNCTION STARTED")
    print("-"*80)
    temp_folder = os.path.join(folder_path, "extracted")
    if not os.path.exists(temp_folder):
        os.makedirs(temp_folder)
        print(f"📁 Created extraction folder: {temp_folder}")
    else:
        print(f"📁 Extraction folder already exists: {temp_folder}")
    zip_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.zip')]
    print(f"🔍 Found {len(zip_files)} zip file(s) in {folder_path}")

    for file in zip_files:
        zip_path = os.path.join(folder_path, file)
        zip_name = os.path.splitext(file)[0]
        extract_subfolder = os.path.join(temp_folder, zip_name)

        print(f"\n📦 Processing zip file: {file}")
        if os.path.exists(extract_subfolder) and os.listdir(extract_subfolder):
            print(f"   ⚠️ Zip file {file} already extracted")
            pdf_files_in_subfolder = [f for f in os.listdir(extract_subfolder) if f.lower().endswith('.pdf')]
            for extracted_file in pdf_files_in_subfolder:
                extracted_path = os.path.join(extract_subfolder, extracted_file)
                extracted_files.append(extracted_path)
            continue

        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                if not os.path.exists(extract_subfolder):
                    os.makedirs(extract_subfolder)
                zip_ref.extractall(extract_subfolder)

                pdf_files_in_subfolder = [f for f in os.listdir(extract_subfolder) if f.lower().endswith('.pdf')]
                for extracted_file in pdf_files_in_subfolder:
                    extracted_path = os.path.join(extract_subfolder, extracted_file)
                    extracted_files.append(extracted_path)

            print(f"   ✅ Successfully extracted {file}")
        except Exception as e:
            print(f"   ❌ Error extracting {file}: {str(e)}")

    return extracted_files

# def send_data_to_api(json_file_path, api_url=None):
#     """Send the generated JSON data to the specified API endpoint"""
#     if not api_url:
#         api_url = DEFAULT_API_URL

#     if not api_url:
#         print("⚠️ No API URL provided, skipping API upload")
#         return False

#     print(f"\n🌐 Sending data to API: {api_url}")

#     try:
#         with open(json_file_path, 'r', encoding='utf-8') as f:
#             json_data = json.load(f)

#         print(f"📄 Loaded JSON data from: {json_file_path}")
#         invoices_array = []
#         if "invoice" in json_data:
#             for pdf_key, pages_data in json_data["invoice"].items():
#                 for page_data in pages_data:
#                     if "IMEI/Serial Numbers" in page_data:
#                         imei_data = page_data["IMEI/Serial Numbers"]
#                         if isinstance(imei_data, list):
#                             if imei_data:
#                                 page_data["IMEI_Serial_Numbers"] = ", ".join(str(imei) for imei in imei_data)
#                                 page_data["IMEI"] = ", ".join(str(imei) for imei in imei_data)
#                             else:
#                                 page_data["IMEI_Serial_Numbers"] = ""
#                                 page_data["IMEI"] = ""
#                         else:
#                             page_data["IMEI_Serial_Numbers"] = str(imei_data) if imei_data else ""
#                             page_data["IMEI"] = str(imei_data) if imei_data else ""
#                     else:
#                         page_data["IMEI_Serial_Numbers"] = ""
#                         page_data["IMEI"] = ""
#                     invoices_array.append(page_data)

#         api_data = {
#             "timestamp": json_data.get("timestamp", ""),
#             "processing_date": json_data.get("processing_date", ""),
#             "total_pdfs_processed": json_data.get("total_pdfs_processed", 0),
#             "total_zip_files_extracted": json_data.get("total_zip_files_extracted", 0),
#             "total_pdfs_from_zip": json_data.get("total_pdfs_from_zip", 0),
#             "invoices_folder": json_data.get("invoices_folder", ""),
#             "invoice": json_data.get("invoice", {}),
#             "invoices": invoices_array
#         }

#         headers = {
#             'Content-Type': 'application/json',
#             'User-Agent': 'PDF-Extractor/1.0'
#         }

#         response = requests.post(api_url, json=api_data, headers=headers, timeout=DEFAULT_API_TIMEOUT)

#         if response.status_code == 200:
#             print("✅ Data successfully sent to API!")
#             return True
#         else:
#             print(f"❌ API request failed with status code: {response.status_code}")
#             return False

#     except Exception as e:
#         print(f"❌ Error sending data to API: {str(e)}")
#         return False

def extract_perfect_data(folder_path=None, api_url=None, api_key=None):
    """Extract data perfectly matching the actual PDF structure from specified folder"""
    print("="*80)
    print("🎯 ENHANCED PDF EXTRACTOR WITH TABULA-PY")
    print("="*80)
    print("Extracting data with enhanced product name detection...")

    if folder_path is None:
        invoice_folder = "Invoice"
    else:
        invoice_folder = folder_path
    if not os.path.exists(invoice_folder):
        print(f"❌ Error: Folder '{invoice_folder}' does not exist!")
        return None

    print(f"📁 Reading PDFs from folder: {invoice_folder}")

    results = {
        "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
        "processing_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "total_pdfs_processed": 0,
        "total_zip_files_extracted": 0,
        "total_pdfs_from_zip": 0,
        "invoices_folder": invoice_folder,
        "invoice": {}
    }

    # Extract zip files if any
    print(f"🔍 Checking for zip files in folder: {invoice_folder}")
    extracted_pdf_files = extract_zip_files(invoice_folder)
    pdf_files = []

    try:
        for file in os.listdir(invoice_folder):
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(invoice_folder, file))
        pdf_files.extend(extracted_pdf_files)

    except Exception as e:
        print(f"❌ Error reading folder '{invoice_folder}': {str(e)}")
        return None

    if not pdf_files:
        print(f"❌ No PDF files found in folder '{invoice_folder}' (including extracted zip files)")
        return None

    print(f"📄 Found {len(pdf_files)} PDF file(s) to process (including {len(extracted_pdf_files)} from zip files)")
    results["total_pdfs_from_zip"] = len(extracted_pdf_files)

    temp_folder = os.path.join(invoice_folder, "extracted")
    if os.path.exists(temp_folder):
        zip_subfolders = [d for d in os.listdir(temp_folder) if os.path.isdir(os.path.join(temp_folder, d))]
        results["total_zip_files_extracted"] = len(zip_subfolders)

    for pdf_path in pdf_files:
        print(f"\n🔍 Processing: {os.path.basename(pdf_path)}")

        try:
            pdf_data = extract_pdf_enhanced(pdf_path)
            if pdf_data:
                pdf_key = f"__{os.path.basename(pdf_path).replace('.pdf', '_pdf')}"
                results["invoice"][pdf_key] = pdf_data
                results["total_pdfs_processed"] += 1
                for page in pdf_data:
                    product_title = page.get('Product Title', 'N/A')
                    product = page.get('Product', 'N/A')
                    gross = page.get('Gross Amount', 'N/A')
                    total = page.get('Grand Total', 'N/A')
                    print(f"✅ Page {page['Page']}: Product Title='{product_title}', Product='{product}', Gross={gross}, Total={total}")
        except Exception as e:
            print(f"❌ Error processing {os.path.basename(pdf_path)}: {str(e)}")

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"enhanced_extracted_data_{timestamp}.json"

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=4, ensure_ascii=False)

    print(f"\n🎉 Enhanced extraction complete!")
    print(f"📄 Output: {output_file}")
    print(f"📊 Total PDFs processed: {results['total_pdfs_processed']}")
    print(f"📦 Total ZIP files extracted: {results['total_zip_files_extracted']}")
    print(f"📄 Total PDFs from ZIP files: {results['total_pdfs_from_zip']}")

    # # Send data to API
    # api_success = send_data_to_api(output_file, api_url)
    # if api_success:
    #     print("🌐 ✅ Data successfully sent to API!")
    # else:
    #     print("🌐 ⚠️ Failed to send data to API (data still saved locally)")

    # return output_file

def extract_pdf_enhanced(pdf_path):
    """Enhanced PDF extraction with tabula-py integration"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            pages_data = []

            for page_num, page in enumerate(pdf_reader.pages):
                text = page.extract_text()
                if not text:
                    continue

                print(f"   📄 Extracting page {page_num + 1}...")
                data = {
                    "Page": page_num + 1,
                    "Customer Name": "",
                    "Shipping Address": "",
                    "Billing Address": "",
                    "Order ID": "",
                    "Order Date": "",
                    "Invoice Date": "",
                    "Invoice Number": "",
                    "PAN": "",
                    "CIN": "",
                    "GSTIN": "",
                    "IRN": "",
                    "Sold By": "",
                    "Product Title": "",
                    "Product": "",
                    "Quantity": "",
                    "Gross Amount": "",
                    "Discount": "",
                    "Taxable Value": "",
                    "IGST": "",
                    "SGST": "",
                    "Grand Total": "",
                    "HSN/SAC": "",
                    "IMEI/Serial Numbers": [],
                    "Source PDF": os.path.basename(pdf_path).replace('.pdf', ''),
                    "Source Folder": os.path.dirname(pdf_path),
                    "Full Path": pdf_path
                }

                # Extract all fields using enhanced methods
                extract_enhanced_fields(text, data, pdf_path, page_num + 1)
                pages_data.append(data)

            return pages_data

    except Exception as e:
        print(f"   ❌ Error reading PDF: {str(e)}")
        return None

def extract_product_enhanced(text, pdf_path, page_num):
    """Enhanced product extraction from 'Product' column using tabula-py"""
    print(f"   🔍 Enhanced product extraction from 'Product' column for page {page_num}...")

    # First try tabula-py for better table structure recognition
    tabula_result = extract_product_with_tabula(pdf_path, page_num)
    if tabula_result:
        return tabula_result

    # Fallback to text-based extraction for 'Product' column
    print("   📝 Falling back to text-based extraction for 'Product' column...")
    return extract_product_from_text(text)

def extract_product_from_text(text):
    """Extract product from text when 'Product' header is found"""
    if not text:
        return ""

    # Try vertical scanning for 'Product' header
    lines = text.split('\n')
    product_headers = ['product']

    for i, line in enumerate(lines):
        line_lower = line.lower().strip()
        for header in product_headers:
            if (header == line_lower and len(line_lower.strip()) <= 10):
                # Look for product in next few lines
                for j in range(i + 1, min(i + 10, len(lines))):
                    if j >= len(lines):
                        break

                    candidate_line = lines[j].strip()
                    if not candidate_line:
                        continue

                    # Skip unwanted patterns
                    skip_words = ['hsn', 'sac', 'qty', 'gross', 'discount', 'taxable', 'igst', 'total']
                    if any(skip_word in candidate_line.lower() for skip_word in skip_words):
                        continue

                    if (re.search(r'[A-Za-z]', candidate_line) and
                        len(candidate_line) > 10 and
                        not re.search(r'^\d+\s*%', candidate_line)):

                        cleaned_candidate = clean_product_title(candidate_line)
                        if is_valid_product_title(cleaned_candidate) and len(cleaned_candidate) > 5:
                            return cleaned_candidate

    return ""

def extract_enhanced_fields(text, data, pdf_path, page_num):
    """Extract fields with enhanced product title detection"""

    # Enhanced product title extraction using tabula-py
    product_title = extract_product_title_enhanced(text, pdf_path, page_num)
    if product_title:
        data["Product Title"] = product_title
        print(f"   ✅ Product Title: {product_title}")

    # Enhanced product extraction from 'Product' column using tabula-py
    product = extract_product_enhanced(text, pdf_path, page_num)
    if product:
        data["Product"] = product
        print(f"   ✅ Product: {product}")

    # Use original extraction methods for other fields
    # (Import the functions from your original last_final.py)

    # Order ID
    order_match = re.search(r'Order\s+(?:Id|ID)[:\s]*\n?\s*(OD\d+)', text, re.IGNORECASE)
    if order_match:
        data["Order ID"] = order_match.group(1)
        print(f"   ✅ Order ID: {data['Order ID']}")

    # Invoice Number
    invoice_no_patterns = [
        r": ([A-Z0-9]{13,16}) Invoice Number",
        r": ([A-Z0-9]{13,16})\s*\n\s*Invoice Number",
        r"Invoice Number: ([A-Z0-9]{13,16})",
        r"Invoice Number\s*\n\s*:\s*([A-Z0-9]{13,16})",
        r"Invoice Number\s*:\s*([A-Z0-9]{13,16})",
        r"Invoice Number #\s*([A-Z0-9]+)(?:Tax|$)",
        r"Invoice No[:\s]+([A-Z0-9-]+)",
        r"Invoice Number[:\s]+#?\s*([A-Z0-9-]+)"
    ]

    print("Looking for invoice number in text section:")
    text_sample = text[:1000] if len(text) > 1000 else text
    print(text_sample)

    for pattern in invoice_no_patterns:
        invoice_no_match = re.search(pattern, text, re.IGNORECASE)
        if invoice_no_match:
            invoice_number = invoice_no_match.group(1)
            data["Invoice Number"] = invoice_number
            print(f"Found invoice number: {invoice_number}")
            break

    # Extract other fields using simplified patterns
    # (You can add more extraction functions from your original file here)

    # GSTIN
    gstin_patterns = [
        r"GSTIN\s*-\s*([0-9A-Z]+)",
        r"GSTIN[:\s]+([0-9A-Z]+)",
        r"GST IN[:\s]+([0-9A-Z]+)"
    ]

    for pattern in gstin_patterns:
        gstin_match = re.search(pattern, text)
        if gstin_match:
            data["GSTIN"] = gstin_match.group(1)
            break

    # PAN
    pan_patterns = [
        r"PAN[:\s]+([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s*:\s*([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s*-\s*([A-Z]{5}[0-9]{4}[A-Z])",
        r"\s+PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s+No[:\s]+([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s+Number[:\s]+([A-Z]{5}[0-9]{4}[A-Z])",
        r"Permanent\s+Account\s+Number[:\s]+([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN:?\s*\n\s*([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s+([A-Z]{5}[0-9]{4}[A-Z])",
        # Pattern for just the PAN number format (use cautiously)
        r"\b([A-Z]{5}[0-9]{4}[A-Z])\b"
    ]

    # Print a section of the text to help with debugging PAN
    print("\n" + "="*50)
    print("DEBUGGING PAN EXTRACTION")
    print("="*50)
    print("Looking for PAN in text section:")
    text_sample = text[:1000] if len(text) > 1000 else text
    print(text_sample)

    # First check if there's a PAN explicitly labeled
    pan_found = False

# Check if the text contains GSTIN
    print("\nChecking for GSTIN...")
    gstin_pattern = r"GSTIN\s*[-:]*\s*([0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][0-9A-Z]{1,3})"
    gstin_match = re.search(gstin_pattern, text)
    gstin_found = False

    # Find all GSTINs in the document
    for gstin_match in re.finditer(gstin_pattern, text):
        gstin_found = True
        gstin = gstin_match.group(1)
        print(f"Found GSTIN: {gstin}")
        data["GSTIN"] = gstin
        break  # Just use the first GSTIN found

    if not gstin_found:
        print("No GSTIN found in the text")

    # Always try to extract PAN if it's explicitly labeled as PAN in the document
    # This way we extract PAN if it's present, regardless of whether GSTIN is found
    print("\nLooking for PAN in the document...")
    data["PAN"] = ""  # Initialize PAN to empty string
    pan_found = False

    # First, try to find PAN in the format "PAN: **********"
    print("Looking for PAN in the format 'PAN: **********'...")

    # Try all patterns in order of specificity (most specific first)
    for i, pattern in enumerate(pan_patterns):
        print(f"Trying pattern {i+1}: {pattern}")
        pan_match = re.search(pattern, text)
        if pan_match:
            # Verify this is a valid PAN format (5 letters, 4 numbers, 1 letter)
            potential_pan = pan_match.group(1)
            print(f"  Found potential PAN: {potential_pan}")

            if re.match(r"^[A-Z]{5}[0-9]{4}[A-Z]$", potential_pan):
                print(f"  Valid PAN format: {potential_pan}")

                data["PAN"] = potential_pan
                print(f"  ✅ Using PAN: {data['PAN']}")
                pan_found = True
                break
            else:
                print(f"  Invalid PAN format: {potential_pan}")
        else:
            print(f"  No match found for pattern {i+1}")

    # If we still haven't found a PAN, try a more aggressive approach
    if not pan_found:
        print("Trying more aggressive PAN extraction...")
        # Look for any text that matches the PAN format (10 characters, 5 letters, 4 numbers, 1 letter)
        pan_format_pattern = r"\b([A-Z]{5}[0-9]{4}[A-Z])\b"
        all_pan_matches = re.findall(pan_format_pattern, text)

        if all_pan_matches:
            # Filter out any matches that are part of a GSTIN
            gstin_pattern = r"[0-9]{2}([A-Z]{5}[0-9]{4}[A-Z])[0-9A-Z]{1,3}"
            gstin_matches = re.findall(gstin_pattern, text)

            # Find PANs that are not part of GSTINs
            valid_pans = [pan for pan in all_pan_matches if pan not in gstin_matches]

            if valid_pans:
                data["PAN"] = valid_pans[0]  # Use the first valid PAN found
                print(f"  ✅ Found standalone PAN: {data['PAN']}")
                pan_found = True

    # If PAN is not found, leave it as an empty string
    if not pan_found:
        print("PAN not found in the document, setting to empty string")
        data["PAN"] = ""

    # Customer Name - comprehensive patterns
    customer_patterns = [
        r"Customer Name[:\s]+([^\n]+)",
        r"Bill To[:\s]+([^\n]+)",
        r"Billing Address[:\s]+([^\n]+)",
        r"Ship To[:\s]+([^\n]+)"
    ]

    for pattern in customer_patterns:
        customer_match = re.search(pattern, text)
        if customer_match:
            data["Customer Name"] = customer_match.group(1).strip()
            print(f"   ✅ Customer Name: {data['Customer Name']}")
            break

    # Order Date - try multiple patterns
    order_date_patterns = [
        r"(\d{2}-\d{2}-\d{4})\s+Order Date[:]",
        r"Order Date[:\s]+(\d{2}[/-]\d{2}[/-]\d{4})",
        r"Order Date[:\s]+(\d{1,2}\s+[A-Za-z]+\s+\d{4})",
        r"Order Date:\s*(\d{2}-\d{2}-\d{4})"
    ]

    for pattern in order_date_patterns:
        order_date_match = re.search(pattern, text)
        if order_date_match:
            data["Order Date"] = order_date_match.group(1)
            break

    # Invoice Date
    print("Looking for invoice date in text section:")
    text_sample = text[:1000] if len(text) > 1000 else text
    print(text_sample)

    invoice_date_patterns = [
        r"Invoice Date:\s*(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Invoice Date[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Date of Invoice[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Invoice Date[:\s]+(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})",
        r"Date of Invoice[:\s]+(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})",
        r"Invoice Date[:\s]+(\d{4}[-/.]\d{1,2}[-/.]\d{1,2})",
        r"Date of Invoice[:\s]+(\d{4}[-/.]\d{1,2}[-/.]\d{1,2})",
        r"Invoice\s+(?:No|Number)[^,]*,?\s*dated\s+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Invoice\s+(?:No|Number)[^,]*,?\s*dated\s+(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})",
        r"Date:\s*(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Date\s+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Invoice.*?\n.*?Date[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"(?:Invoice|Tax)\s+(?:Invoice|Date)[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Bill Date[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Invoice.*?Date[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Dated[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Dated[:\s]+(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})"
    ]

    for pattern in invoice_date_patterns:
        invoice_date_match = re.search(pattern, text, re.IGNORECASE)
        if invoice_date_match:
            invoice_date = invoice_date_match.group(1).strip()
            data["Invoice Date"] = invoice_date
            print(f"Found invoice date: {invoice_date} with pattern: {pattern}")
            break

    if "Invoice Date" not in data or not data["Invoice Date"]:
        invoice_positions = [m.start() for m in re.finditer(r"invoice", text, re.IGNORECASE)]

        for pos in invoice_positions:
            context_start = max(0, pos - 100)
            context_end = min(len(text), pos + 100)
            context = text[context_start:context_end]
            date_patterns = [
                r"(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",  # DD/MM/YYYY, MM/DD/YYYY
                r"(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})",    # DD Month YYYY
                r"(\d{4}[-/.]\d{1,2}[-/.]\d{1,2})"      # YYYY/MM/DD
            ]

            for pattern in date_patterns:
                date_match = re.search(pattern, context)
                if date_match:
                    invoice_date = date_match.group(1).strip()
                    data["Invoice Date"] = invoice_date
                    print(f"Found invoice date from context: {invoice_date}")
                    break

            if "Invoice Date" in data and data["Invoice Date"]:
                break

    if ("Invoice Date" not in data or not data["Invoice Date"]) and "Order Date" in data and data["Order Date"]:
        data["Invoice Date"] = data["Order Date"]
        print(f"Using Order Date as fallback for Invoice Date: {data['Order Date']}")

    if "Invoice Date" in data and data["Invoice Date"]:
        try:
            # Try to parse the date in various formats
            date_formats = [
                "%d-%m-%Y", "%d/%m/%Y", "%d.%m.%Y",
                "%m-%d-%Y", "%m/%d/%Y", "%m.%d.%Y",
                "%Y-%m-%d", "%Y/%m/%d", "%Y.%m.%d",
                "%d %b %Y", "%d %B %Y",
                "%b %d %Y", "%B %d %Y",
                "%d-%m-%y", "%d/%m/%y", "%d.%m.%y",
                "%m-%d-%y", "%m/%d/%y", "%m.%d.%y",
                "%y-%m-%d", "%y/%m/%d", "%y.%m.%d"
            ]

            parsed_date = None
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(data["Invoice Date"], fmt)
                    break
                except ValueError:
                    continue

            if parsed_date:
                data["Invoice Date"] = parsed_date.strftime("%d-%m-%Y")
                print(f"Standardized invoice date format: {data['Invoice Date']}")
        except Exception as e:
            print(f"Error standardizing date format: {str(e)}")

    # Extract business codes
    gstin_code = extract_gstin(text)
    if gstin_code:
        data["GSTIN"] = gstin_code
        print(f"   ✅ GSTIN: {data['GSTIN']}")

    pan_code = extract_pan(text)
    if pan_code:
        data["PAN"] = pan_code
        print(f"   ✅ PAN: {data['PAN']}")

    cin_code = extract_cin(text)
    if cin_code:
        data["CIN"] = cin_code
        print(f"   ✅ CIN: {data['CIN']}")

    irn_code = extract_irn(text)
    if irn_code:
        data["IRN"] = irn_code
        print(f"   ✅ IRN: {data['IRN']}")

    # Extract addresses
    bill_address_patterns = [
        r"Bill\s+(.*?)Phone:",
        r"Billing Address[:\s]+(.*?)(?=Shipping Address|Phone:|Description)",
        r"Bill To[:\s]+(.*?)(?=Ship To|Phone:)",
        r"Billing Address\s+(.*?)(?=\n\n|Description|Phone:)"
    ]

    for pattern in bill_address_patterns:
        bill_to_match = re.search(pattern, text, re.DOTALL)
        if bill_to_match:
            billing_address = bill_to_match.group(1).replace('\n', ', ').strip()

            in_mh_match = re.search(r"(.*?(?:IN-MH|MH-IN))", billing_address)
            if in_mh_match:
                billing_address = in_mh_match.group(1).strip()
            else:
                postal_code_match = re.search(r"(.*?\d{6})", billing_address)
                if postal_code_match:
                    billing_address = postal_code_match.group(1).strip()

            for ending in ["Seller Registered Address", "Declaration", "Shipping ADDRESS", "FSSAI License", "null"]:
                ending_match = re.search(f"(.*?)(?:{ending})", billing_address)
                if ending_match:
                    billing_address = ending_match.group(1).strip()
                    break

            if len(billing_address) > 200:
                city_state_match = re.search(r"(.*?(?:Pune|Mumbai|Bengaluru|Karnataka|Maharashtra|Delhi|Chennai|Kolkata|Hyderabad|Ahmedabad|Surat)(?:\s*[-,]\s*\d{6})?)", billing_address)
                if city_state_match:
                    billing_address = city_state_match.group(1).strip()
            billing_address = re.sub(r'[,\s.]+$', '', billing_address)

            data["Billing Address"] = billing_address
            break

    # Shipping Address
    shipping_address = extract_shipping_address(text)
    if shipping_address:
        data["Shipping Address"] = shipping_address
        print(f"   ✅ Shipping Address: {data['Shipping Address']}")

    # Sold By
    sold_by_patterns = [
        r'Sold By[:\s]*\n?\s*([^\n,]+)',
        r'Sold By[:\s]*([^,\n]+)'
    ]

    for pattern in sold_by_patterns:
        sold_by_match = re.search(pattern, text, re.IGNORECASE)
        if sold_by_match:
            data["Sold By"] = sold_by_match.group(1).strip()
            print(f"   ✅ Sold By: {data['Sold By']}")
            break

    # HSN/SAC
    hsn_sac_code = extract_hsn_sac(text)
    if hsn_sac_code:
        data["HSN/SAC"] = hsn_sac_code
        print(f"   ✅ HSN/SAC: {data['HSN/SAC']}")

    print(f"   💰 Extracting financial data...")

    # Financial data extraction
    quantity = extract_quantity(text)
    if quantity:
        data["Quantity"] = quantity
        print(f"   ✅ Quantity: {data['Quantity']}")

    gross_amount = extract_gross_amount(text)
    if gross_amount:
        data["Gross Amount"] = gross_amount
        print(f"   ✅ Gross Amount: {data['Gross Amount']}")

    discount = extract_discount(text)
    if discount:
        data["Discount"] = discount
        print(f"   ✅ Discount: {data['Discount']}")

    taxable_value = extract_taxable_value(text)
    if taxable_value:
        data["Taxable Value"] = taxable_value
        print(f"   ✅ Taxable Value: {data['Taxable Value']}")

    igst = extract_igst(text)
    if igst:
        data["IGST"] = igst
        print(f"   ✅ IGST: {data['IGST']}")

    sgst = extract_sgst(text)
    if sgst:
        data["SGST"] = sgst
        print(f"   ✅ SGST: {data['SGST']}")

    grand_total = extract_grand_total(text)
    if grand_total:
        data["Grand Total"] = grand_total
        print(f"   ✅ Grand Total: {data['Grand Total']}")

    # IMEI/Serial Numbers
    imei_numbers = extract_imei_numbers(text)
    if imei_numbers:
        data["IMEI/Serial Numbers"] = imei_numbers
        print(f"   ✅ IMEI/Serial Numbers: {data['IMEI/Serial Numbers']}")
    else:
        data["IMEI/Serial Numbers"] = []

    # Fallback for Grand Total
    if not data.get("Grand Total"):
        total_price_match = re.search(r'TOTAL PRICE[:\s]*(\d+\.\d{2})', text, re.IGNORECASE)
        if total_price_match:
            data["Grand Total"] = float(total_price_match.group(1))
            print(f"   ✅ Grand Total (from TOTAL PRICE): {data['Grand Total']}")

# Now add all the extraction functions from last_final.py

def extract_hsn_sac(text):
    """Extract HSN/SAC code from text using multiple patterns"""
    print("Looking for HSN/SAC codes in text...")
    hsn_sac_patterns = [
        r"HSN/SAC:\s*(\d+)",
        r"HSN/SAC[:\s]+(\d+)",
        r"HSN\s*Code[:\s]+(\d+)",
        r"SAC\s*Code[:\s]+(\d+)",
        r"HSN[:\s]+(\d+)",
        r"SAC[:\s]+(\d+)",
        r"HSN/SAC\s*:\s*(\d+)",
        r"HSN\s*:\s*(\d+)",
        r"SAC\s*:\s*(\d+)",
        r"HSN/SAC\s*-\s*(\d+)",
        r"HSN\s*-\s*(\d+)",
        r"SAC\s*-\s*(\d+)",
        r"HSN/SAC.*?\n.*?(\d+)",
        r"HSN.*?\n.*?(\d+)",
        r"SAC.*?\n.*?(\d+)",
        r"HSN/SAC:(\d+)",
        r"HSN/SAC\s*:\s*(\d+)(?:[A-Z]|\s)",
        r"FSN:.*?HSN/SAC:\s*(\d+)",
        r"HSN/SAC:\s*(\d+)[^\d\s]",
        r"Product Title.*?HSN/SAC:?\s*(\d+)",
        r"Description.*?HSN/SAC:?\s*(\d+)",
        r"Item.*?HSN/SAC:?\s*(\d+)",
        r"HSN/SAC\s*\n\s*(\d+)",
        r"HSN/SAC:?\s*(\d+).*?(\d+\.?\d*)\s*%",
        r"HSN/SAC.*?(\d{4,10})",
        r"HSN\s*\(\s*(\d+)\s*\)",
        r"HSN\s*/\s*(\d+)",
        r"HSN\s*=\s*(\d+)",
        r"HSN\s*:\s*(\d+)",
        r"HSN\s*\|\s*(\d+)",
        r"HSN\s*-\s*(\d+)",
        r"HSN:(\d+)",
        r"HSN\s+(\d+)",
        r"HSN.*?(\d{4,10})"
    ]

    for pattern in hsn_sac_patterns:
        hsn_sac_match = re.search(pattern, text, re.IGNORECASE)
        if hsn_sac_match:
            hsn_sac_code = hsn_sac_match.group(1).strip()
            print(f"Found HSN/SAC code: {hsn_sac_code} with pattern: {pattern}")
            return hsn_sac_code

    print("No HSN/SAC code found")
    return None

def extract_pan(text):
    """Extract PAN code from text using multiple patterns and strategies"""
    print("Looking for PAN codes in text...")
    pan_patterns = [
        r"PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN[:\s]+([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*:\s*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*-\s*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*=\s*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN:([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN.*?\n.*?([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*(?:No|Number)[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*\(\s*([A-Z]{5}[0-9]{4}[A-Z]{1})\s*\)",
        r"PAN\s*/\s*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*\|\s*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s+([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN[:\s]*([A-Z]{5}\.?[0-9]{4}\.?[A-Z]{1})",
        r"Company.*?PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"Seller.*?PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"Tax.*?PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"(?:Company|Seller|Tax|GST).*?PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"Address.*?PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"PAN\s*\n\s*([A-Z]{5}[0-9]{4}[A-Z]{1})",
        r"\b([A-Z]{5}[0-9]{4}[A-Z])\b"
    ]

    for pattern in pan_patterns:
        pan_match = re.search(pattern, text, re.IGNORECASE)
        if pan_match:
            pan_code = pan_match.group(1).strip().upper()
            if re.match(r'^[A-Z]{5}[0-9]{4}[A-Z]$', pan_code):
                print(f"Found PAN code: {pan_code} with pattern: {pattern}")
                return pan_code

    print("No PAN code found")
    return None

def extract_gstin(text):
    """Extract GSTIN code from text using multiple patterns and strategies"""
    print("Looking for GSTIN codes in text...")
    gstin_patterns = [
        r"GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN[:\s]+([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*:\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*-\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*=\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN:([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN.*?\n.*?([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*(?:No|Number)[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*\(\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])\s*\)",
        r"GSTIN\s*/\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*\|\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s+([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN[:\s]*([0-9]{2}\.?[A-Z0-9]{10}\.?[0-9]\.?[A-Z]\.?[A-Z0-9])",
        r"Company.*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"Seller.*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"Tax.*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"(?:Company|Seller|Tax|GST).*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"Address.*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN\s*\n\s*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"GSTIN.*?([0-9A-Z]{15})",
        r"Billing.*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"Shipping.*?GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])",
        r"\b([0-9]{2}[A-Z0-9]{13})\b"
    ]

    for pattern in gstin_patterns:
        gstin_match = re.search(pattern, text, re.IGNORECASE)
        if gstin_match:
            gstin_code = gstin_match.group(1).strip().upper()
            if re.match(r'^[0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9]$', gstin_code) and len(gstin_code) == 15:
                print(f"Found GSTIN code: {gstin_code} with pattern: {pattern}")
                return gstin_code

    print("No GSTIN code found")
    return None

def extract_invoice_date(text):
    """Extract Invoice Date from text using patterns based on actual PDF analysis"""
    print("Looking for Invoice Date in text...")

    invoice_date_patterns = [
        r"(\d{2}-\d{2}-\d{4})\s+Invoice Date:",
        r"(\d{1,2}-\d{1,2}-\d{4})\s+Invoice Date:",
        r"(\d{2}/\d{2}/\d{4})\s+Invoice Date:",
        r"(\d{1,2}/\d{1,2}/\d{4})\s+Invoice Date:",
        r"Invoice Date:\s*\n?\s*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date:\s*\n?\s*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date:\s*\n?\s*(\d{2}/\d{2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date:\s*\n?\s*(\d{1,2}/\d{1,2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date[:\s]+(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date[:\s]+(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date[:\s]+(\d{2}/\d{2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date[:\s]+(\d{1,2}/\d{1,2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date\s*\n\s*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date\s*\n\s*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date\s*\n\s*(\d{2}/\d{2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice Date\s*\n\s*(\d{1,2}/\d{1,2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Inv\.?\s*Date[:\s]*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Inv\.?\s*Date[:\s]*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Date of Invoice[:\s]*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Date of Invoice[:\s]*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Tax Invoice.*?Invoice Date[:\s]*\n?\s*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Tax Invoice.*?Invoice Date[:\s]*\n?\s*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice.*?Date[:\s]*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Invoice.*?Date[:\s]*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"(\d{2}\.\d{2}\.\d{4})\s+Invoice Date:",
        r"Invoice Date[:\s]*(\d{2}\.\d{2}\.\d{4})",
        r"Invoice Date[:\s]*(\d{1,2}\.\d{1,2}\.\d{4})",
        r"Invoice Date.*?(\d{1,2}[-/\.]\d{1,2}[-/\.]\d{4})",
    ]

    for pattern in invoice_date_patterns:
        date_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if date_match:
            invoice_date = date_match.group(1).strip()
            # Clean up the date (remove time if present for consistency)
            if ',' in invoice_date:
                invoice_date = invoice_date.split(',')[0].strip()
            print(f"Found Invoice Date: {invoice_date} with pattern: {pattern}")
            return invoice_date

    print("No Invoice Date found")
    return None

def extract_order_date(text):
    """Extract Order Date from text using patterns based on actual PDF analysis"""
    print("Looking for Order Date in text...")
    order_date_patterns = [
        r"(\d{2}-\d{2}-\d{4})\s+Order Date:",
        r"(\d{1,2}-\d{1,2}-\d{4})\s+Order Date:",
        r"(\d{2}/\d{2}/\d{4})\s+Order Date:",
        r"(\d{1,2}/\d{1,2}/\d{4})\s+Order Date:",
        r"Order Date:\s*\n?\s*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date:\s*\n?\s*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date:\s*\n?\s*(\d{2}/\d{2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date:\s*\n?\s*(\d{1,2}/\d{1,2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date[:\s]+(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date[:\s]+(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date[:\s]+(\d{2}/\d{2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date[:\s]+(\d{1,2}/\d{1,2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date\s*\n\s*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date\s*\n\s*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date\s*\n\s*(\d{2}/\d{2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order Date\s*\n\s*(\d{1,2}/\d{1,2}/\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order\.?\s*Date[:\s]*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order\.?\s*Date[:\s]*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Date of Order[:\s]*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Date of Order[:\s]*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Purchase Order.*?Order Date[:\s]*\n?\s*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Purchase Order.*?Order Date[:\s]*\n?\s*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order.*?Date[:\s]*(\d{2}-\d{2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"Order.*?Date[:\s]*(\d{1,2}-\d{1,2}-\d{4}(?:,\s*\d{1,2}:\d{2}\s*[AP]M)?)",
        r"(\d{2}\.\d{2}\.\d{4})\s+Order Date:",
        r"Order Date[:\s]*(\d{2}\.\d{2}\.\d{4})",
        r"Order Date[:\s]*(\d{1,2}\.\d{1,2}\.\d{4})",
        r"Order Date\s*-\s*(\d{2}-\d{2}-\d{4})",
        r"Order Date\s*=\s*(\d{2}-\d{2}-\d{4})",
        r"Order Date\s*-\s*(\d{1,2}-\d{1,2}-\d{4})",
        r"Order Date\s*=\s*(\d{1,2}-\d{1,2}-\d{4})",
        r"Order Date\s*\(\s*(\d{2}-\d{2}-\d{4})\s*\)",
        r"Order Date\s*\(\s*(\d{1,2}-\d{1,2}-\d{4})\s*\)",
        r"Order Date\s*/\s*(\d{2}-\d{2}-\d{4})",
        r"Order Date\s*/\s*(\d{1,2}-\d{1,2}-\d{4})",
        r"Order Date\s*\|\s*(\d{2}-\d{2}-\d{4})",
        r"Order Date\s*\|\s*(\d{1,2}-\d{1,2}-\d{4})",
        r"Order Date.*?(\d{1,2}[-/\.]\d{1,2}[-/\.]\d{4})",
    ]

    for pattern in order_date_patterns:
        date_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if date_match:
            order_date = date_match.group(1).strip()
            if ',' in order_date:
                order_date = order_date.split(',')[0].strip()
            print(f"Found Order Date: {order_date} with pattern: {pattern}")
            return order_date

    print("No Order Date found")
    return None

def extract_cin(text):
    """Extract CIN code from text"""
    print("Looking for CIN codes in text...")
    cin_patterns = [
        r"CIN[:\s]*([A-Z0-9]{21})",
        r"CIN[:\s]+([A-Z0-9]{21})",
        r"CIN\s*:\s*([A-Z0-9]{21})",
        r"CIN\s*-\s*([A-Z0-9]{21})",
        r"CIN:([A-Z0-9]{21})",
        r"CIN.*?\n.*?([A-Z0-9]{21})",
        r"CIN\s*(?:No|Number)[:\s]*([A-Z0-9]{21})",
        r"Company.*?CIN[:\s]*([A-Z0-9]{21})",
        r"([A-Z0-9]{21})",
        r"\b([A-Z0-9]{21})\b"
    ]

    for pattern in cin_patterns:
        cin_match = re.search(pattern, text, re.IGNORECASE)
        if cin_match:
            cin_code = cin_match.group(1).strip().upper()
            if len(cin_code) == 21:
                print(f"Found CIN code: {cin_code} with pattern: {pattern}")
                return cin_code

    print("No CIN code found")
    return None

def extract_irn(text):
    """Extract IRN code from text"""
    print("Looking for IRN codes in text...")
    irn_patterns = [
        r"IRN[:\s]*([A-Z0-9]{64})",
        r"IRN[:\s]+([A-Z0-9]{64})",
        r"IRN\s*:\s*([A-Z0-9]{64})",
        r"IRN\s*-\s*([A-Z0-9]{64})",
        r"IRN:([A-Z0-9]{64})",
        r"IRN.*?\n.*?([A-Z0-9]{64})",
        r"IRN\s*(?:No|Number)[:\s]*([A-Z0-9]{64})",
        r"([A-Z0-9]{64})",
        r"\b([A-Z0-9]{64})\b"
    ]

    for pattern in irn_patterns:
        irn_match = re.search(pattern, text, re.IGNORECASE)
        if irn_match:
            irn_code = irn_match.group(1).strip().upper()
            if len(irn_code) == 64:
                print(f"Found IRN code: {irn_code} with pattern: {pattern}")
                return irn_code

    print("No IRN code found")
    return None

def extract_billing_address(text):
    """Extract billing address from text"""
    print("Looking for billing address in text...")
    billing_patterns = [
        r"Bill To[:\s]*\n?\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*Ship To|\n\s*Order|\n\s*Invoice|\n\s*$)",
        r"Billing Address[:\s]*\n?\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*Shipping|\n\s*Order|\n\s*Invoice|\n\s*$)",
        r"Bill To[:\s]*([^,\n]+(?:,[^,\n]+)*)",
        r"Billing[:\s]*([^,\n]+(?:,[^,\n]+)*)"
    ]

    for pattern in billing_patterns:
        address_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if address_match:
            address = address_match.group(1).strip()
            if len(address) > 10:
                print(f"Found billing address: {address[:50]}...")
                return address

    print("No billing address found")
    return None

def extract_shipping_address(text):
    """Extract shipping address from invoice text up to pincode"""
    shipping_patterns = [
        r"Shipping Address[:\s]+(.*?)(?:Phone:|Email:|GSTIN|PAN|Invoice|Order|Total|\n{2,})"
        r"Shipping Address[:\s]*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Billing|Order|Invoice|GSTIN|PAN|CIN|$))",
        r"Ship To[:\s]*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Bill|Order|Invoice|GSTIN|PAN|CIN|$))",
        r"Delivery Address[:\s]*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Billing|Order|Invoice|GSTIN|PAN|CIN|$))",
        r"Shipping Address[:\s]*([^\n]+)",
        r"Ship To[:\s]*([^\n]+)",
        r"Delivery Address[:\s]*([^\n]+)",
        r"Shipping Address:\s*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*[A-Z])",
        r"Ship To:\s*\n\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*[A-Z])",
        r"Shipping Address[:\s]*\n\s*([^\n]+)\s*\n\s*([^\n]+)\s*\n\s*([^\n]+)",
        r"Ship To[:\s]*\n\s*([^\n]+)\s*\n\s*([^\n]+)\s*\n\s*([^\n]+)",
        r"Shipping.*?\n.*?([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Billing|Order|Invoice|$))",
        r"Ship.*?Address[:\s]*([^\n]+)",
        r"Delivery.*?Address[:\s]*([^\n]+)",
    ]

    for pattern in shipping_patterns:
        match = re.search(pattern, text, re.DOTALL)
        if match:
            shipping_raw = match.group(1).strip().replace('\n', ', ')

            # Extract only up to the first 6-digit pincode
            pincode_match = re.search(r"(.*?\b\d{6}\b)", shipping_raw)
            if pincode_match:
                shipping_address = pincode_match.group(1).strip()
            else:
                shipping_address = shipping_raw.split(',')[0]  # fallback to first part

            # Remove trailing commas or spaces
            shipping_address = re.sub(r'[,\s]+$', '', shipping_address)

            return shipping_address

    return ""


def extract_quantity(text):
    print("Looking for Quantity in text...")

    quantity_patterns = [
        r'\|\s*(\d+)\s*\|\s*[\d.]+\s*\|\s*[-\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+',
        r'(\d+)\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'(\d+)\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00
        r'(\d+)\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 499.00 -25.00 451.43 22.57 474.00
        r'Qty[|\s]*(\d+)',
        r'Quantity[:\s]*(\d+)',
        r'Qty[:\s]*(\d+)',
        r'QTY[:\s]*(\d+)',
        r'TOTAL QTY[:\s]*(\d+)',
        r'pack of (\d+)',
        r'(\d+) piece',
        r'(\d+) pcs',
    ]

    for pattern in quantity_patterns:
        qty_match = re.search(pattern, text, re.IGNORECASE)
        if qty_match:
            quantity = int(qty_match.group(1))
            print(f"Found Quantity: {quantity} with pattern: {pattern}")
            return quantity

    print("No Quantity found, defaulting to 1")
    return ""

def extract_gross_amount(text):
    """Extract Gross Amount from text using multiple patterns"""
    print("Looking for Gross Amount in text...")

    gross_patterns = [
        r'\|\s*\d+\s*\|\s*([\d.]+)\s*\|\s*[-\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+',
        r'\d+\s+([\d.]+)\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'\d+\s+([\d.]+)\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00
        r'\d+\s+([\d.]+)\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 499.00 -25.00 451.43 22.57 474.00
        r'Gross\s*Amount[|\s]*(\d+\.\d{2})',
        r'Gross[|\s]*(\d+\.\d{2})',
        r'Gross Amount[:\s]*(\d+\.\d{2})',
        r'Gross[:\s]*(\d+\.\d{2})',
        r'Amount[:\s]*(\d+\.\d{2})',
        r'Amount.*?(\d+\.\d{2})',
    ]

    for pattern in gross_patterns:
        gross_match = re.search(pattern, text, re.IGNORECASE)
        if gross_match:
            gross_amount = float(gross_match.group(1))
            print(f"Found Gross Amount: {gross_amount} with pattern: {pattern}")
            return gross_amount

    print("No Gross Amount found")
    return None

def extract_discount(text):
    """Extract Discount from text using multiple patterns"""
    print("Looking for Discount in text...")

    discount_patterns = [
        r'\|\s*\d+\s*\|\s*[\d.]+\s*\|\s*([-\d.]+)\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+',
        r'\d+\s+[\d.]+\s+([-\d.]+)\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'\d+\s+[\d.]+\s+([-\d.]+)\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00
        r'\d+\s+[\d.]+\s+([-\d.]+)\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 499.00 -25.00 451.43 22.57 474.00
        r'Discount[|\s]*(-?\d+\.\d{2})',
        r'Discount[:\s]*(-?\d+\.\d{2})',
        r'Disc[:\s]*(-?\d+\.\d{2})',
        r'(\d+)% off',
        r'Discount.*?(\d+\.\d{2})',
    ]

    for pattern in discount_patterns:
        discount_match = re.search(pattern, text, re.IGNORECASE)
        if discount_match:
            discount_val = float(discount_match.group(1))
            discount = abs(discount_val)  # Make positive
            print(f"Found Discount: {discount} with pattern: {pattern}")
            return discount

    print("No Discount found, defaulting to 0.0")
    return 0.0  # Default to 0 if not found

def extract_taxable_value(text):
    """Extract Taxable Value from text using multiple patterns"""
    print("Looking for Taxable Value in text...")

    taxable_patterns = [
        r'\|\s*\d+\s*\|\s*[\d.]+\s*\|\s*[-\d.]+\s*\|\s*([\d.]+)\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+',
        r'\d+\s+[\d.]+\s+[-\d.]+\s+([\d.]+)\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'\d+\s+[\d.]+\s+[-\d.]+\s+([\d.]+)\s+[\d.]+\s+[\d.]+\s+[\d.]+',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00
        r'\d+\s+[\d.]+\s+[-\d.]+\s+([\d.]+)\s+[\d.]+\s+[\d.]+',  # 1 499.00 -25.00 451.43 22.57 474.00
        r'Taxable\s*Value[|\s]*(\d+\.\d{2})',
        r'Taxable[|\s]*(\d+\.\d{2})',
        r'Taxable Value[:\s]*(\d+\.\d{2})',
        r'Taxable[:\s]*(\d+\.\d{2})',
        r'Tax Base[:\s]*(\d+\.\d{2})',
        r'Taxable Amount[:\s]*(\d+\.\d{2})',
    ]

    for pattern in taxable_patterns:
        taxable_match = re.search(pattern, text, re.IGNORECASE)
        if taxable_match:
            taxable_value = float(taxable_match.group(1))
            print(f"Found Taxable Value: {taxable_value} with pattern: {pattern}")
            return taxable_value

    print("No Taxable Value found")
    return None

def extract_igst(text):
    """Extract IGST from text using multiple patterns"""
    print("Looking for IGST in text...")

    igst_patterns = [
        r'\|\s*\d+\s*\|\s*[\d.]+\s*\|\s*[-\d.]+\s*\|\s*[\d.]+\s*\|\s*([\d.]+)\s*\|\s*[\d.]+\s*\|\s*[\d.]+',
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+([\d.]+)\s+[\d.]+\s+[\d.]+',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+([\d.]+)\s+[\d.]+\s+[\d.]+',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00 (IGST position)
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+([\d.]+)\s+[\d.]+',  # 1 499.00 -25.00 451.43 22.57 474.00 (IGST position)
        r'IGST[|\s]*(\d+\.\d{2})',
        r'IGST[:\s]*(\d+\.\d{2})',
        r'IGST.*?(\d+\.\d{2})',
        r'Integrated GST[:\s]*(\d+\.\d{2})',
        r'IGST:\s*(\d+\.\d{2})%',
        r'IGST\s*@\s*\d+%[:\s]*(\d+\.\d{2})',
    ]

    for pattern in igst_patterns:
        igst_match = re.search(pattern, text, re.IGNORECASE)
        if igst_match:
            igst_value = float(igst_match.group(1))
            print(f"Found IGST: {igst_value} with pattern: {pattern}")
            return igst_value

    print("No IGST found, defaulting to 0.0")
    return 0.0  # Default to 0 if not found

def extract_sgst(text):
    """Extract SGST from text using multiple patterns"""
    print("Looking for SGST in text...")

    sgst_patterns = [
        r'\|\s*\d+\s*\|\s*[\d.]+\s*\|\s*[-\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*([\d.]+)\s*\|\s*[\d.]+',
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+([\d.]+)\s+[\d.]+',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+([\d.]+)\s+[\d.]+',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00 (SGST position)
        r'SGST[|\s]*(\d+\.\d{2})',
        r'SGST[:\s]*(\d+\.\d{2})',
        r'SGST.*?(\d+\.\d{2})',
        r'State GST[:\s]*(\d+\.\d{2})',
        r'SGST:\s*(\d+\.\d{2})%',
        r'SGST\s*@\s*\d+%[:\s]*(\d+\.\d{2})',
        r'CGST[:\s]*(\d+\.\d{2})',
        r'Central GST[:\s]*(\d+\.\d{2})',
    ]

    for pattern in sgst_patterns:
        sgst_match = re.search(pattern, text, re.IGNORECASE)
        if sgst_match:
            sgst_value = float(sgst_match.group(1))
            print(f"Found SGST: {sgst_value} with pattern: {pattern}")
            return sgst_value

    print("No SGST found, defaulting to 0.0")
    return 0.0  # Default to 0 if not found


def extract_grand_total(text):
    """Extract Grand Total from text using multiple patterns"""
    print("Looking for Grand Total in text...")

    total_patterns = [
        r'\|\s*\d+\s*\|\s*[\d.]+\s*\|\s*[-\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*[\d.]+\s*\|\s*([\d.]+)',
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+([\d.]+)',  # 4 188.00 -60.00 114.28 13.72 0.00 128.00
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+[\d.]+\s+([\d.]+)',  # 1 699.00 -0.00 592.38 53.31 53.31 699.00 (Total position)
        r'\d+\s+[\d.]+\s+[-\d.]+\s+[\d.]+\s+[\d.]+\s+([\d.]+)',  # 1 499.00 -25.00 451.43 22.57 474.00 (Total position)
        r'TOTAL PRICE[:\s]*(\d+\.\d{2})',
        r'Total[|\s]*(\d+\.\d{2})',
        r'Grand Total[:\s]*(\d+\.\d{2})',
        r'Total Amount[:\s]*(\d+\.\d{2})',
        r'Final Total[:\s]*(\d+\.\d{2})',
        r'Net Total[:\s]*(\d+\.\d{2})',
        r'Total[:\s]*(\d+\.\d{2})',
        r'Invoice Total[:\s]*(\d+\.\d{2})',
        r'Bill Total[:\s]*(\d+\.\d{2})',
        r'Total.*?(\d+\.\d{2})',
    ]

    for pattern in total_patterns:
        total_match = re.search(pattern, text, re.IGNORECASE)
        if total_match:
            grand_total = float(total_match.group(1))
            print(f"Found Grand Total: {grand_total} with pattern: {pattern}")
            return grand_total

    print("No Grand Total found")
    return None

def extract_imei_numbers(text):
    print("Looking for IMEI/Serial numbers in text...")
    imei_numbers = []
    invoice_number_match = re.search(r"Invoice Number[:\s]+([A-Z0-9-]+)", text, re.IGNORECASE)
    invoice_number = invoice_number_match.group(1) if invoice_number_match else ""
    # Clean invoice number to remove "Tax" suffix if present
    invoice_number = re.sub(r'Tax$', '', invoice_number, flags=re.IGNORECASE).strip()
    order_id_match = re.search(r"Order ID[:\s]+([A-Z0-9-]+)", text, re.IGNORECASE)
    order_id = order_id_match.group(1) if order_id_match else ""
    print(f"Found Invoice Number: {invoice_number} and Order ID: {order_id} - will exclude these from IMEI detection")
    SIMILARITY_THRESHOLD = 0.7

    def is_too_similar_to_invoice(imei, invoice_num):
        if not invoice_num:
            return False

        similarity = similar_strings(imei, invoice_num)
        if similarity > SIMILARITY_THRESHOLD:
            print(f"Rejecting potential IMEI {imei} - too similar to invoice number {invoice_num} (similarity: {similarity:.2f})")
            return True
        return False
    imei_patterns = [
        r"IMEI/Serial No[:\s]*(.*?)(?=\n\n|\Z)",
        r"IMEI/Serial\s+No[:\s]*(.*?)(?=\n\n|\Z)",
        r"IMEI[:\s]*(.*?)(?=\n\n|\Z)",
        r"Serial No[:\s]*(.*?)(?=\n\n|\Z)"
    ]

    specific_pattern = re.search(r'\d+\.\s*\[?IMEI/Serial\s*No:?\s*([A-Z0-9][A-Z0-9\s]{8,20})', text, re.IGNORECASE)
    if specific_pattern:
        imei_with_spaces = specific_pattern.group(1)
        imei = re.sub(r'\s+', '', imei_with_spaces)
        if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
            print(f"Found IMEI with specific pattern: {imei} (original: {imei_with_spaces})")
            return [imei]

    bracket_pattern = re.search(r'\]\s*([A-Z0-9][A-Z0-9\s]{8,15}?)(?=\n|$)', text)
    if bracket_pattern:
        imei_with_spaces = bracket_pattern.group(1)
        imei = re.sub(r'\s+', '', imei_with_spaces)
        if (imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number) and
            not re.match(r'^FSN[A-Z0-9]+$', imei) and
            not re.match(r'^[A-Z0-9]+FSN$', imei) and
            not re.match(r'^[A-Z]{3,}[0-9]{6,}$', imei) and
            "FSN" not in imei):
            print(f"Found IMEI with bracket pattern: {imei} (original: {imei_with_spaces})")
            if re.search(r'[A-Z][0-9]|[0-9][A-Z]', imei) and not imei.isalpha() and not imei.isdigit():
                return [imei]

    imei_section_match = re.search(r"IMEI/Serial No[:\s]*(.*?)(?=\n\n|\Z)", text, re.DOTALL | re.IGNORECASE)
    if imei_section_match:
        imei_section = imei_section_match.group(1)
        print(f"Found IMEI section: {imei_section}")
        alpha_imei_match = re.search(r'([A-Z0-9][A-Z0-9\s]{8,20})', imei_section)
        if alpha_imei_match:
            imei_with_spaces = alpha_imei_match.group(1)
            imei = re.sub(r'\s+', '', imei_with_spaces)
            if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
                if (re.search(r'[A-Z][0-9]|[0-9][A-Z]', imei) and
                    not imei.isalpha() and
                    not imei.isdigit() and
                    not re.match(r'\d{2}[A-Z]{5}\d{4}[A-Z]\d?Z\w', imei) and
                    not re.match(r'^OD[0-9A-Z]+$', imei) and
                    not re.match(r'^FSN[A-Z0-9]+$', imei) and
                    not re.match(r'^[A-Z0-9]+FSN$', imei) and
                    not re.match(r'^[A-Z]{3,}[0-9]{6,}$', imei) and
                    "FSN" not in imei):
                    print(f"Found alphanumeric IMEI: {imei} (original: {imei_with_spaces})")
                    return [imei]

        imeis = []
        first_imei_match = re.search(r"(\d{11})\s+(\d{4})", imei_section)

        if first_imei_match:
            first_imei = first_imei_match.group(1) + first_imei_match.group(2)
            if first_imei != invoice_number and first_imei != order_id and not is_too_similar_to_invoice(first_imei, invoice_number):
                imeis.append(first_imei)
        additional_imeis = re.findall(r"\d{14,15}", imei_section)

        filtered_imeis = []
        for imei in additional_imeis:
            if imei != order_id and imei != invoice_number and not is_too_similar_to_invoice(imei, invoice_number):
                filtered_imeis.append(imei)
        imeis.extend(filtered_imeis)
        seen = set()
        imeis = [x for x in imeis if not (x in seen or seen.add(x))]
        if imeis:
            return imeis

    print("Looking for specific IMEI format in the entire document...")

    imei_list_match = re.search(r"IMEI/Serial No:\s*,\s*([\d\s,]+)]", text, re.IGNORECASE)
    if imei_list_match:
        imei_list_text = imei_list_match.group(1)
        individual_imeis = re.findall(r"(\d{10,15})", imei_list_text)
        filtered_imeis = []
        for imei in individual_imeis:
            if imei != order_id and imei != invoice_number and not is_too_similar_to_invoice(imei, invoice_number):
                filtered_imeis.append(imei)

        if filtered_imeis:
            print(f"Found {len(filtered_imeis)} IMEIs from list format")
            return filtered_imeis

    imei_block_match = re.search(r"\[IMEI/Serial No:\s*,(.*?)\]", text, re.DOTALL | re.IGNORECASE)
    if imei_block_match:
        imei_block = imei_block_match.group(1)
        individual_imeis = re.findall(r"(\d{10,15})", imei_block)
        filtered_imeis = []
        for imei in individual_imeis:
            if imei != order_id and imei != invoice_number and not is_too_similar_to_invoice(imei, invoice_number):
                filtered_imeis.append(imei)

        if filtered_imeis:
            print(f"Found {len(filtered_imeis)} IMEIs from block format")
            return filtered_imeis

    potential_imeis = re.findall(r'SMG\d+\s*[A-Z]+(?:\s*[A-Z0-9]+)?', text)
    if potential_imeis:
        for potential_imei in potential_imeis:
            imei = re.sub(r'\s+', '', potential_imei)
            if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
                if 10 <= len(imei) <= 15:
                    print(f"Found potential SMG IMEI: {imei} (original: {potential_imei})")
                    return [imei]

    standard_imei_patterns = [
        r"IMEI[:\s]+(\d{15})",
        r"Serial Number[:\s]+(\d{15})",
        r"IMEI Number[:\s]+(\d{15})",
        r"Serial No[:\s]+(\d{15})"
    ]
    for pattern in standard_imei_patterns:
        for match in re.finditer(pattern, text):
            imei = match.group(1)
            if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
                if len(imei) == 15 and imei.isdigit():
                    imei_numbers.append(imei)

    if imei_numbers:
        return imei_numbers
    print("No specific IMEI format found, searching for any potential IMEI...")
    potential_imeis = re.findall(r"[A-Z0-9]{10,15}", text)

    for imei in potential_imeis:
        if (imei != order_id and
            imei != invoice_number and
            not re.match(r"^\d{6}$", imei) and
            not re.match(r"^[A-Z]{5}\d{4}[A-Z]$", imei) and
            not re.match(r'\d{2}[A-Z]{5}\d{4}[A-Z]\d?Z\w', imei) and
            not re.match(r'^OD[0-9A-Z]+$', imei) and
            not re.match(r'^U\d+[A-Z]+\d+', imei) and
            not re.match(r'^FA[A-Z0-9]+$', imei) and
            not re.match(r'^FB[A-Z0-9]+$', imei) and
            not re.match(r'^BF[A-Z0-9]+$', imei) and
            not re.match(r'^EA[A-Z0-9]+$', imei) and
            not re.match(r'^FSN[A-Z0-9]+$', imei) and
            not re.match(r'^[A-Z0-9]+FSN$', imei) and
            not re.match(r'^[A-Z]{3,}[0-9]{6,}$', imei) and
            not re.match(r'^[0-9]{6,}[A-Z]{3,}$', imei) and
            not imei.isalpha() and
            not imei.isdigit() and
            re.search(r'[A-Z][0-9]|[0-9][A-Z]', imei) and
            "GSTIN" not in imei and
            "CIN" not in imei and
            "Invoice" not in imei and
            "FSN" not in imei):

            if invoice_number and (invoice_number in imei or imei in invoice_number):
                print(f"Skipping potential IMEI {imei} as it appears to be related to invoice number {invoice_number}")
                continue
            if is_too_similar_to_invoice(imei, invoice_number):
                continue
            if re.search(r'FSN|SKU|ASIN|PRODUCT|CODE', text[max(0, text.find(imei)-50):text.find(imei)+50], re.IGNORECASE):
                print(f"Skipping potential IMEI {imei} as it appears to be near FSN/SKU/product code context")
                continue

            print(f"Found potential IMEI in document: {imei}")
            return [imei]
    print("No IMEI numbers found in the document, leaving IMEI field empty")
    return []

# Main execution
if __name__ == "__main__":
    import sys

    folder_path = sys.argv[1] if len(sys.argv) > 1 else None
    api_url = sys.argv[2] if len(sys.argv) > 2 else None

    result = extract_perfect_data(folder_path, api_url)
    if result:
        print(f"\n🎉 Processing completed successfully!")
        print(f"📁 Output file: {result}")
    else:
        print("\n❌ Processing failed!")
        sys.exit(1)