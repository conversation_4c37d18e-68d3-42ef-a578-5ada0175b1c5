import tabula
import pandas as pd

pdf_path = "OD330725585114150100.pdf"

# Step 1: Extract tables using tabula
tables = tabula.read_pdf(pdf_path, pages="all", multiple_tables=True, lattice=True)

# Step 2: Use first table (adjust if needed)
invoice_table = tables[0]

if invoice_table.shape[1] == 1:
    # Step 3: Split row by whitespace
    split_rows = invoice_table.iloc[:, 0].str.split(r'\s+', expand=True)

    # Step 4: Recombine columns from the right
    cleaned_rows = []
    for _, row in split_rows.iterrows():
        if len(row) < 8:
            continue  # Skip bad rows

        # Extract last 7 columns as numeric fields
        total = row.iloc[-1]
        sgst = row.iloc[-2]
        cgst = row.iloc[-3]
        taxable_value = row.iloc[-4]
        discount = row.iloc[-5]
        gross_amount = row.iloc[-6]
        qty = row.iloc[-7]

        # Everything before that is description
        description = " ".join(row.iloc[:-7].dropna())

        cleaned_rows.append([
            description, qty, gross_amount, discount,
            taxable_value, cgst, sgst, total
        ])

    # Step 5: Create DataFrame
    df = pd.DataFrame(cleaned_rows, columns=[
        "Product Description", "Qty", "Gross Amount", "Discount",
        "Taxable Value", "CGST", "SGST/UTGST", "Total"
    ])

    print("\n✅ Final Cleaned Table:\n")
    print(df)

    # Save to CSV
    df.to_csv("final_invoice_table.csv", index=False)

else:
    print("✅ Table looks structured already:")
    print(invoice_table)
