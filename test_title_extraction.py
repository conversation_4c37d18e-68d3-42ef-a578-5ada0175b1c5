#!/usr/bin/env python3

import pandas as pd
from enhanced_final import extract_product_from_title_column, is_likely_product_text, extract_multiline_title_from_text, extract_shipping_address

def test_title_column_extraction():
    """Test the new Title column extraction functionality"""
    print("="*80)
    print("TESTING ENHANCED PRODUCT NAME AND SHIPPING ADDRESS EXTRACTION")
    print("="*80)

    # Test case 1: DataFrame with Title column containing multi-line product name
    print("\n📋 Test Case 1: DataFrame with Title column")
    test_data = {
        'Title': ['Title', 'Apple MacBook AIR Apple', 'M2 - (8 GB/256 GB SSD', '/Mac OS Monterey)', 'HSN: 84713000'],
        'Qty': ['Qty', '1', '', '', ''],
        'Gross': ['Gross', '499.00', '', '', '']
    }

    df1 = pd.DataFrame(test_data)
    print("DataFrame:")
    print(df1)

    result1 = extract_product_from_title_column(df1)
    print(f"Result: {result1}")

    # Test case 2: DataFrame with Product name column
    print("\n📋 Test Case 2: DataFrame with Product name column")
    test_data2 = {
        'Product name': ['Product name', 'Samsung Galaxy S23', 'Ultra 5G (256GB)', 'Phantom Black', 'HSN: 85171200'],
        'Qty': ['Qty', '1', '', '', ''],
        'Price': ['Price', '89999.00', '', '', '']
    }

    df2 = pd.DataFrame(test_data2)
    print("DataFrame:")
    print(df2)

    result2 = extract_product_from_title_column(df2)
    print(f"Result: {result2}")

    # Test case 3: Text-based extraction with Title header
    print("\n📋 Test Case 3: Text-based extraction with Title header")
    test_text = """
Invoice Details
Title
Apple MacBook AIR Apple
M2 - (8 GB/256 GB SSD
/Mac OS Monterey)
HSN: 84713000
Qty: 1
Gross: 499.00
"""

    lines = test_text.strip().split('\n')
    title_index = -1
    for i, line in enumerate(lines):
        if line.strip().lower() == 'title':
            title_index = i
            break

    if title_index >= 0:
        result3 = extract_multiline_title_from_text(lines, title_index)
        print(f"Result: {result3}")
    else:
        print("Title header not found in text")

    # Test case 4: Text-based extraction with Product name header
    print("\n📋 Test Case 4: Text-based extraction with Product name header")
    test_text2 = """
Invoice Details
Product name
Samsung Galaxy S23
Ultra 5G (256GB)
Phantom Black
HSN: 85171200
Qty: 1
Price: 89999.00
"""

    lines2 = test_text2.strip().split('\n')
    product_name_index = -1
    for i, line in enumerate(lines2):
        if line.strip().lower() == 'product name':
            product_name_index = i
            break

    if product_name_index >= 0:
        result4 = extract_multiline_title_from_text(lines2, product_name_index)
        print(f"Result: {result4}")
    else:
        print("Product name header not found in text")

    # Test case 5: Shipping Address extraction
    print("\n📋 Test Case 5: Shipping Address extraction")
    shipping_test_text = """
Bill To:
John Doe
123 Main Street
New York, NY 10001

Ship To:
Jane Smith
456 Oak Avenue
Los Angeles, CA 90210

Order Details:
Product: Test Product
"""

    shipping_result = extract_shipping_address(shipping_test_text)
    print(f"Shipping Address Result: {shipping_result}")

    # Test case 6: Test is_likely_product_text function
    print("\n📋 Test Case 6: Testing is_likely_product_text function")
    test_strings = [
        "Apple MacBook AIR Apple",
        "M2 - (8 GB/256 GB SSD",
        "/Mac OS Monterey)",
        "Samsung Galaxy S23",
        "Ultra 5G (256GB)",
        "Phantom Black",
        "HSN: 84713000",
        "Qty: 1",
        "499.00",
        "IGST: 18%"
    ]

    for test_str in test_strings:
        is_product = is_likely_product_text(test_str)
        print(f"'{test_str}' -> {is_product}")

if __name__ == "__main__":
    test_title_column_extraction()
