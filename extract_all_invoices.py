import tabula
import os
import json
import re
import pandas as pd
import glob

# Set JAVA_HOME for tabula-py
os.environ['JAVA_HOME'] = os.path.join(os.getcwd(), 'jdk-11.0.2')
os.environ['PATH'] = os.path.join(os.getcwd(), 'jdk-11.0.2', 'bin') + os.pathsep + os.environ.get('PATH', '')

def clean_text(text):
    """Clean extracted text"""
    if not text:
        return ""
    # Remove extra whitespace and clean up
    text = re.sub(r'\s+', ' ', str(text))
    text = re.sub(r'[^\w\s\-\(\)\.\/,]', ' ', text)
    return text.strip()

def extract_invoice_data(tables, page_num, pdf_filename):
    """Extract specific invoice information from tables"""
    invoice_data = {
        "Page": page_num,
        "Customer Name": "",
        "Shipping Address": "",
        "Billing Address": "",
        "Order ID": "",
        "Order Date": "",
        "Invoice Date": "",
        "Invoice Number": "",
        "PAN": "",
        "CIN": "",
        "GSTIN": "",
        "IRN": "",
        "Sold By": "",
        "Product Title": "",
        "Quantity": 0,
        "Taxable Value": 0.0,
        "IGST": 0.0,
        "Grand Total": 0.0,
        "HSN/SAC": "",
        "IMEI/Serial Numbers": [],
        "Source PDF": pdf_filename.replace('.pdf', ''),
        "Source Folder": ".\\Invoice",
        "Full Path": f".\\Invoice\\{pdf_filename}"
    }
    
    # Combine all table data into one text for easier parsing
    all_text = ""
    for df in tables:
        if not df.empty:
            # Convert dataframe to string and add to all_text
            for col in df.columns:
                for val in df[col].dropna():
                    if pd.notna(val):
                        all_text += str(val) + " "
    
    # Extract information using regex patterns
    patterns = {
        "Order ID": r"(?:Order\s*ID|Order\s*No|Order\s*Number|OD)[\s:]*([A-Z0-9]{10,})",
        "Invoice Number": r"(?:Invoice\s*No|Invoice\s*Number|Invoice\s*#)[\s:]*([A-Z0-9]{8,})",
        "Invoice Date": r"(?:Invoice\s*Date|Date\s*of\s*Invoice)[\s:]*(\d{1,2}[-/]\d{1,2}[-/]\d{4})",
        "Order Date": r"(?:Order\s*Date|Date\s*of\s*Order)[\s:]*(\d{1,2}[-/]\d{1,2}[-/]\d{4})",
        "PAN": r"(?:PAN|P\.A\.N)[\s:]*([A-Z0-9]{10})",
        "GSTIN": r"(?:GSTIN|GST\s*IN|GST\s*No)[\s:]*([A-Z0-9]{15})",
        "HSN/SAC": r"(?:HSN|SAC)[\s:]*(\d{6,8})",
        "Sold By": r"(?:Sold\s*By|Seller)[\s:]*([A-Z\s&\.\-]+?)(?:\s*\n|\s*,|$)",
    }
    
    # Extract using patterns
    for field, pattern in patterns.items():
        match = re.search(pattern, all_text, re.IGNORECASE)
        if match:
            invoice_data[field] = clean_text(match.group(1))
    
    # Extract customer name
    customer_patterns = [
        r"(?:Bill\s*To|Ship\s*To)[\s:]*([A-Za-z\s,\.]+?)(?:\s*,|\s*\n|\s*\d|$)",
        r"(?:Customer\s*Name|Name)[\s:]*([A-Za-z\s,\.]+?)(?:\s*,|\s*\n|$)",
    ]
    
    for pattern in customer_patterns:
        customer_match = re.search(pattern, all_text, re.IGNORECASE)
        if customer_match and len(customer_match.group(1).strip()) > 2:
            invoice_data["Customer Name"] = clean_text(customer_match.group(1))
            break
    
    # Extract addresses
    address_patterns = [
        r"([A-Za-z0-9\s,\-\(\)\.\/]+\d{6}[A-Za-z0-9\s,\-\(\)\.\/]*)",  # Pattern with pincode
        r"([A-Za-z0-9\s,\-\(\)\.\/]+(?:Building|Road|Street|Avenue|Lane|Apartment|Flat|Block|Society|Complex|Tower|Plot|House)[A-Za-z0-9\s,\-\(\)\.\/]*)",
    ]
    
    for pattern in address_patterns:
        address_match = re.search(pattern, all_text, re.IGNORECASE)
        if address_match and len(address_match.group(1).strip()) > 10:
            address = clean_text(address_match.group(1))
            if len(address) < 200:  # Reasonable address length
                invoice_data["Shipping Address"] = address
                invoice_data["Billing Address"] = address
                break
    
    # Extract numerical values
    amount_patterns = {
        "Taxable Value": r"(?:Taxable\s*Value|Taxable\s*Amount)[\s:]*(\d+\.?\d*)",
        "IGST": r"(?:IGST)[\s:]*(\d+\.?\d*)",
        "Grand Total": r"(?:Grand\s*Total|Total\s*Amount)[\s:]*(\d+\.?\d*)",
        "Quantity": r"(?:Qty|Quantity)[\s:]*(\d+)"
    }
    
    for field, pattern in amount_patterns.items():
        match = re.search(pattern, all_text, re.IGNORECASE)
        if match:
            try:
                if field == "Quantity":
                    invoice_data[field] = int(match.group(1))
                else:
                    invoice_data[field] = float(match.group(1))
            except ValueError:
                pass
    
    # Extract product title (clean up the longest meaningful text)
    product_lines = re.findall(r"([A-Za-z][A-Za-z\s\-\(\)0-9]{20,})", all_text)
    if product_lines:
        # Take the longest line as product title and clean it
        product_title = max(product_lines, key=len).strip()
        # Remove common unwanted patterns
        product_title = re.sub(r'(?:HSN|SAC|IGST|CGST|SGST).*', '', product_title)
        product_title = clean_text(product_title)
        if len(product_title) > 10:
            invoice_data["Product Title"] = product_title
    
    return invoice_data

def process_pdf(pdf_path):
    """Process a single PDF file"""
    print(f"\n📄 Processing: {pdf_path}")
    
    try:
        # Get number of pages
        from PyPDF2 import PdfReader
        pdf = PdfReader(pdf_path)
        num_pages = len(pdf.pages)
        
        # Get PDF filename without path
        pdf_filename = os.path.basename(pdf_path)
        
        # Store all invoice data for this PDF
        pdf_invoice_data = []
        
        # Extract and save table from each page
        for page in range(1, num_pages + 1):
            try:
                # Try to extract all areas of the page
                all_areas = []
                
                # Try lattice method first
                tables = tabula.read_pdf(pdf_path, pages=page, multiple_tables=True, lattice=True)
                all_areas.extend(tables)

                # If no tables found with lattice, try stream method
                if not tables or all(df.empty for df in tables):
                    print(f"📝 Page {page}: Trying stream method...")
                    tables = tabula.read_pdf(pdf_path, pages=page, multiple_tables=True, stream=True)
                    all_areas.extend(tables)

                # If still no tables, try guess method
                if not tables or all(df.empty for df in tables):
                    print(f"📝 Page {page}: Trying guess method...")
                    tables = tabula.read_pdf(pdf_path, pages=page, multiple_tables=True, guess=True)
                    all_areas.extend(tables)
                
                # Try to extract entire page
                try:
                    entire_page = tabula.read_pdf(pdf_path, pages=page, pandas_options={'header': None})
                    if entire_page and not entire_page[0].empty:
                        all_areas.extend(entire_page)
                except:
                    pass
                
                # Extract structured invoice data
                invoice_data = extract_invoice_data(all_areas, page, pdf_filename)
                pdf_invoice_data.append(invoice_data)

                tables_found = len([df for df in tables if not df.empty])
                if tables_found > 0:
                    print(f"✅ Page {page}: Extracted {tables_found} table(s)")
                else:
                    print(f"⚠️ Page {page}: No tables detected")

            except Exception as e:
                print(f"❌ Error processing page {page}: {e}")
                # Add empty data for failed pages
                invoice_data = extract_invoice_data([], page, pdf_filename)
                pdf_invoice_data.append(invoice_data)
        
        return pdf_invoice_data
        
    except Exception as e:
        print(f"❌ Error processing PDF {pdf_path}: {e}")
        return []

# Main execution
if __name__ == "__main__":
    # Directory to store JSON files
    output_dir = "output_json"
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all PDF files in Invoice directory
    pdf_files = glob.glob("Invoice/*.pdf")
    
    if not pdf_files:
        print("❌ No PDF files found in Invoice directory")
        exit(1)
    
    print(f"🔍 Found {len(pdf_files)} PDF files to process")
    
    # Store all invoice data
    all_invoices = {}
    
    # Process each PDF
    for pdf_path in pdf_files:
        pdf_data = process_pdf(pdf_path)
        if pdf_data:
            pdf_name = os.path.basename(pdf_path).replace('.pdf', '')
            all_invoices[f"__{pdf_name}_pdf"] = pdf_data
    
    # Save consolidated data
    consolidated_data = {"invoice": all_invoices}
    
    output_file = f"{output_dir}/all_invoices_consolidated.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(consolidated_data, f, indent=4, ensure_ascii=False)
    
    print(f"\n🎉 Processing complete!")
    print(f"📁 Processed {len(all_invoices)} PDF files")
    print(f"💾 Consolidated data saved to: {output_file}")
