#!/usr/bin/env python3

import os
import json
import re
import pandas as pd
import tabula
from datetime import datetime
from PyPDF2 import PdfReader

# Set JAVA_HOME for tabula-py
os.environ['JAVA_HOME'] = os.path.join(os.getcwd(), 'jdk-11.0.2')
os.environ['PATH'] = os.path.join(os.getcwd(), 'jdk-11.0.2', 'bin') + os.pathsep + os.environ.get('PATH', '')

def extract_table_data_with_tabula(pdf_path, page_num):
    """Extract table data from PDF using tabula-py for unstructured tables"""
    print(f"   🔍 Using tabula-py to extract table data from page {page_num}...")

    try:
        # Try different extraction methods for better table recognition
        all_tables = []

        # Method 1: Lattice extraction (best for tables with clear borders)
        try:
            print("   📊 Trying lattice extraction...")
            tables = tabula.read_pdf(pdf_path, pages=page_num, multiple_tables=True, lattice=True)
            if tables:
                all_tables.extend(tables)
                print(f"   ✅ Lattice extraction found {len(tables)} table(s)")
        except Exception as e:
            print(f"   ⚠️ Lattice extraction failed: {str(e)}")

        # Method 2: Stream extraction (best for tables without clear borders)
        try:
            print("   📊 Trying stream extraction...")
            tables = tabula.read_pdf(pdf_path, pages=page_num, multiple_tables=True, stream=True)
            if tables:
                all_tables.extend(tables)
                print(f"   ✅ Stream extraction found {len(tables)} table(s)")
        except Exception as e:
            print(f"   ⚠️ Stream extraction failed: {str(e)}")

        # Method 3: Guess extraction (automatic detection)
        try:
            print("   📊 Trying guess extraction...")
            tables = tabula.read_pdf(pdf_path, pages=page_num, multiple_tables=True, guess=True)
            if tables:
                all_tables.extend(tables)
                print(f"   ✅ Guess extraction found {len(tables)} table(s)")
        except Exception as e:
            print(f"   ⚠️ Guess extraction failed: {str(e)}")

        # Method 4: Extract entire page as table
        try:
            print("   📊 Trying entire page extraction...")
            entire_page = tabula.read_pdf(pdf_path, pages=page_num, pandas_options={'header': None})
            if entire_page:
                all_tables.extend(entire_page)
                print(f"   ✅ Entire page extraction found {len(entire_page)} table(s)")
        except Exception as e:
            print(f"   ⚠️ Entire page extraction failed: {str(e)}")

        if not all_tables:
            print("   ❌ No tables found with any extraction method")
            return {}

        print(f"   📋 Total tables extracted: {len(all_tables)}")

        # Process all extracted tables to find the best one with product data
        best_table_data = {}
        max_data_score = 0

        for i, df in enumerate(all_tables):
            if df.empty:
                continue

            print(f"\n   📊 Processing table {i+1}/{len(all_tables)}...")
            print(f"   📏 Table shape: {df.shape}")

            # Extract data from this table
            table_data = extract_data_from_dataframe(df, i+1)

            # Score the table based on how much relevant data it contains
            data_score = calculate_table_score(table_data)
            print(f"   📊 Table {i+1} score: {data_score}")

            if data_score > max_data_score:
                max_data_score = data_score
                best_table_data = table_data
                print(f"   🏆 Table {i+1} is now the best table (score: {data_score})")

        if best_table_data:
            print(f"   ✅ Best table data extracted with score: {max_data_score}")
            return best_table_data
        else:
            print("   ⚠️ No relevant table data found")
            return {}

    except Exception as e:
        print(f"   ❌ Error using tabula-py: {str(e)}")
        return {}

def extract_data_from_dataframe(df, table_num):
    """Extract data from a pandas DataFrame and organize it into key-value pairs"""
    print(f"   🔍 Analyzing table {table_num} structure...")

    # Initialize the result dictionary
    extracted_data = {
        "Product": [],
        "Description": [],
        "Title": [],
        "Qty": [],
        "Gross Amount": [],
        "Discount": [],
        "Taxable Value": [],
        "IGST": [],
        "SGST": [],
        "CGST": [],
        "CESS": [],
        "Total": []
    }

    try:
        # Print table structure for debugging
        print(f"   📋 Table columns: {list(df.columns)}")
        print(f"   📏 Table shape: {df.shape}")

        # Clean column names and identify relevant columns
        column_mapping = identify_columns(df)
        print(f"   🎯 Column mapping: {column_mapping}")

        # Extract data column by column
        for field_name, column_info in column_mapping.items():
            if column_info['column'] is not None:
                column_data = extract_column_data(df, column_info['column'], field_name)
                extracted_data[field_name] = column_data
                if column_data:
                    print(f"   ✅ {field_name}: {len(column_data)} items extracted")

        return extracted_data

    except Exception as e:
        print(f"   ❌ Error extracting data from DataFrame: {str(e)}")
        return extracted_data

def identify_columns(df):
    """Identify which columns correspond to which data fields"""
    column_mapping = {
        "Product": {"column": None, "patterns": ["product"]},
        "Description": {"column": None, "patterns": ["description", "desc"]},
        "Title": {"column": None, "patterns": ["title"]},
        "Qty": {"column": None, "patterns": ["qty", "quantity", "quan"]},
        "Gross Amount": {"column": None, "patterns": ["gross", "gross amount", "amount"]},
        "Discount": {"column": None, "patterns": ["discount", "disc"]},
        "Taxable Value": {"column": None, "patterns": ["taxable", "taxable value", "tax value"]},
        "IGST": {"column": None, "patterns": ["igst"]},
        "SGST": {"column": None, "patterns": ["sgst"]},
        "CGST": {"column": None, "patterns": ["cgst"]},
        "CESS": {"column": None, "patterns": ["cess"]},
        "Total": {"column": None, "patterns": ["total", "total amount", "final"]}
    }

    # Check each column against our patterns
    for col in df.columns:
        if pd.notna(col):
            col_str = str(col).lower().strip()

            # Find the best match for this column
            for field_name, field_info in column_mapping.items():
                if field_info["column"] is None:  # Only assign if not already assigned
                    for pattern in field_info["patterns"]:
                        if pattern in col_str:
                            column_mapping[field_name]["column"] = col
                            print(f"   🎯 Mapped '{col}' to '{field_name}'")
                            break

    return column_mapping

def extract_column_data(df, column, field_name):
    """Extract data from a specific column, handling multi-line values and cleaning"""
    print(f"   📊 Extracting data from column '{column}' for field '{field_name}'...")

    try:
        column_data = []

        # Get all non-null values from the column
        for val in df[column].dropna():
            if pd.notna(val):
                val_str = str(val).strip()

                # Skip empty values and header-like values
                if (val_str and
                    val_str.lower() not in [field_name.lower(), 'nan', 'none', ''] and
                    not is_header_value(val_str, field_name)):

                    # For text fields, apply trimming before cleaning (pass field_name for conditional processing)
                    if field_name in ["Product", "Description", "Title"]:
                        val_str = trim_at_keywords(val_str, field_name)
                        # Skip if trimming resulted in empty or very short text
                        if not val_str or len(val_str.strip()) < 3:
                            continue

                    # Clean and validate the value based on field type
                    cleaned_val = clean_field_value(val_str, field_name)
                    if cleaned_val:
                        column_data.append(cleaned_val)

                        # Log trimming for text fields
                        if field_name in ["Product", "Description", "Title"]:
                            original_val = str(df[column].iloc[df[column].dropna().tolist().index(val)]).strip()
                            if len(cleaned_val) < len(original_val) * 0.8:  # If significantly trimmed
                                print(f"   ✂️ Trimmed {field_name}: '{original_val[:30]}...' → '{cleaned_val}'")

        # For product-related fields, try to combine multi-line values
        if field_name in ["Product", "Description", "Title"] and len(column_data) > 1:
            # Check if we should combine the values (for multi-line product names)
            combined_data = combine_multiline_values(column_data, field_name)
            if combined_data:
                return combined_data

        return column_data

    except Exception as e:
        print(f"   ❌ Error extracting column data: {str(e)}")
        return []

def is_header_value(value, field_name):
    """Check if a value is likely a header/column name rather than data"""
    value_lower = value.lower().strip()

    # Common header patterns
    header_patterns = [
        'product', 'description', 'title', 'qty', 'quantity', 'gross', 'amount',
        'discount', 'taxable', 'value', 'igst', 'sgst', 'cgst', 'cess', 'total',
        'hsn', 'sac', 'rate', 'price', 'tax', 'gst'
    ]

    # Check if it's exactly a header pattern
    if value_lower in header_patterns:
        return True

    # Check if it's a short value that looks like a header
    if len(value_lower) <= 15 and any(pattern in value_lower for pattern in header_patterns):
        return True

    return False

def clean_field_value(value, field_name):
    """Clean and validate field values based on their type"""
    if not value or str(value).strip() == '':
        return None

    value_str = str(value).strip()

    # Numeric fields
    if field_name in ["Qty", "Gross Amount", "Discount", "Taxable Value", "IGST", "SGST", "CGST", "CESS", "Total"]:
        return clean_numeric_value(value_str)

    # Text fields (Product, Description, Title) - pass field_name for conditional processing
    elif field_name in ["Product", "Description", "Title"]:
        return clean_text_value(value_str, field_name)

    return value_str

def clean_numeric_value(value):
    """Clean numeric values, extracting numbers from text"""
    if not value:
        return None

    # Remove currency symbols and common text
    cleaned = re.sub(r'[₹$,\s]', '', str(value))
    cleaned = re.sub(r'[^\d.-]', '', cleaned)

    # Try to extract a valid number
    try:
        if '.' in cleaned:
            return float(cleaned)
        else:
            return int(cleaned) if cleaned else None
    except:
        return None

def clean_text_value(value, field_name=None):
    """Clean text values for product names, descriptions, etc."""
    if not value:
        return None

    value_str = str(value).strip()

    # Trim text when encountering HSN, IMEI, IGST keywords and continue with next words
    # Pass field_name to handle shipping charges removal conditionally
    value_str = trim_at_keywords(value_str, field_name)

    # Remove common unwanted patterns
    value_str = re.sub(r'\s+', ' ', value_str)  # Multiple spaces to single space
    value_str = re.sub(r'^[^\w]+|[^\w]+$', '', value_str)  # Leading/trailing non-word chars

    # Skip if too short or looks like metadata
    if len(value_str) < 3:
        return None

    # Skip obvious non-product patterns
    skip_patterns = [
        r'^\d+$',  # Just numbers
        r'^[A-Z0-9]{10,}$',  # Long codes
        r'^\d+\.\d+$',  # Decimal numbers only
        r'^(hsn|sac|gst|tax)[\s:]*\d+',  # Tax codes
    ]

    for pattern in skip_patterns:
        if re.match(pattern, value_str, re.IGNORECASE):
            return None

    return value_str

def trim_at_keywords(text, field_name=None):
    """Skip HSN, IMEI, IGST keywords and their values, then continue with remaining text"""
    if not text:
        return text

    result_text = text

    # Step 1: Handle pipe-separated content
    result_text = process_pipe_content(result_text)

    # Step 2: Remove CESS with its value
    result_text = remove_cess_content(result_text)

    # Step 3: Remove Shipping and Handling Charges ONLY for Product and Title fields
    if field_name and field_name.lower() in ["product", "title"]:
        result_text = remove_shipping_charges(result_text)
        print(f"   🚚 Removing shipping charges from {field_name} field")
    elif field_name and field_name.lower() == "description":
        print(f"   ✅ Keeping shipping charges in Description field")

    # Step 4: Process standard keywords
    skip_keywords = [
        'HSN', 'IMEI', 'IGST', 'SGST', 'CGST', 'SAC', 'FSN', 'SKU',
        'ASIN', 'Model', 'Code', 'Serial', 'SrNo', 'Item Code',
        'GST', 'Tax', 'Rate', 'Qty', 'Quantity'
    ]

    # Process each keyword and remove it along with its value
    for keyword in skip_keywords:
        # More comprehensive patterns to match keyword and its complete associated value
        patterns = [
            # Keyword followed by colon and alphanumeric code (most specific)
            rf'\b{keyword}\b\s*:\s*[A-Z0-9][A-Z0-9\-]*',
            # Keyword followed by space and alphanumeric code
            rf'\b{keyword}\b\s+[A-Z0-9][A-Z0-9\-]*',
            # Keyword followed by number with optional percentage
            rf'\b{keyword}\b\s*:?\s*\d+\.?\d*\s*%?',
            # Keyword followed by any word (fallback)
            rf'\b{keyword}\b\s*:?\s*\S+',
            # Just the keyword with optional colon/spaces
            rf'\b{keyword}\b\s*:?\s*'
        ]

        for pattern in patterns:
            # Find all matches for this pattern
            matches = list(re.finditer(pattern, result_text, re.IGNORECASE))

            # Remove matches from end to beginning to preserve positions
            for match in reversed(matches):
                before = result_text[:match.start()].strip()
                after = result_text[match.end():].strip()
                removed_text = result_text[match.start():match.end()]

                print(f"   🗑️ Removing '{removed_text.strip()}' from text")

                # Combine before and after parts
                if before and after:
                    result_text = before + ' ' + after
                elif before:
                    result_text = before
                elif after:
                    result_text = after
                else:
                    result_text = ''

                # Break after first successful match for this keyword
                break

    # Final cleanup
    result_text = re.sub(r'\s+', ' ', result_text)  # Multiple spaces to single
    result_text = re.sub(r'\s*%\s*', ' ', result_text)  # Remove standalone % symbols
    result_text = re.sub(r'\s*-\s*', ' ', result_text)  # Remove standalone - symbols
    result_text = re.sub(r'\s+', ' ', result_text).strip()  # Final cleanup

    # Show the transformation if text was modified
    if result_text != text:
        print(f"   ✂️ Original: '{text[:100]}...'")
        print(f"   ✅ Cleaned:  '{result_text}'")

    return result_text

def process_pipe_content(text):
    """Process content between pipes - remove numeric/symbol content, keep meaningful text"""
    if '|' not in text:
        return text

    # Split by pipes and process each segment
    segments = text.split('|')
    kept_segments = []

    for i, segment in enumerate(segments):
        segment = segment.strip()

        if i == 0:  # Always keep the first segment (before first pipe)
            kept_segments.append(segment)
        elif i == len(segments) - 1:  # Always keep the last segment (after last pipe)
            if segment:  # Only if not empty
                kept_segments.append(segment)
        else:  # Middle segments - check if they contain meaningful text
            if is_meaningful_text(segment):
                # Extract only the meaningful words from mixed content
                cleaned_segment = extract_meaningful_words(segment)
                if cleaned_segment:
                    kept_segments.append(cleaned_segment)
                    print(f"   ✅ Keeping pipe content: '{cleaned_segment}' (from '{segment}')")
                else:
                    print(f"   🗑️ Removing pipe content: '{segment}'")
            else:
                print(f"   🗑️ Removing pipe content: '{segment}'")

    return ' '.join(kept_segments)

def extract_meaningful_words(text):
    """Extract only meaningful words from text that may contain mixed numbers and text"""
    if not text:
        return ""

    # Split into words and keep only meaningful ones
    words = text.split()
    meaningful_words = []

    for word in words:
        # Keep words that are primarily alphabetic
        if re.search(r'[a-zA-Z]{2,}', word):  # At least 2 consecutive letters
            # Remove numbers and symbols from the word, keep letters
            clean_word = re.sub(r'[^a-zA-Z\s]', '', word).strip()
            if len(clean_word) >= 2:  # Keep if at least 2 letters remain
                meaningful_words.append(clean_word)

    return ' '.join(meaningful_words)

def is_meaningful_text(text):
    """Check if text between pipes contains meaningful content (not just numbers/symbols)"""
    if not text or len(text.strip()) < 3:
        return False

    text = text.strip()

    # Check if it's mostly numbers and dots (financial data)
    if re.match(r'^[\d\.\s]+$', text):
        return False

    # Check if it starts with numbers and has very few letters
    if re.match(r'^[\d\.\s]+[a-zA-Z]', text):
        # Count letters vs numbers
        letters = len(re.findall(r'[a-zA-Z]', text))
        numbers = len(re.findall(r'\d', text))
        if numbers > letters * 3:  # If numbers dominate heavily
            return False

    # Check if there are enough alphabetic characters
    alpha_chars = len(re.findall(r'[a-zA-Z]', text))
    total_chars = len(re.sub(r'\s', '', text))

    # Keep if at least 40% alphabetic characters and some meaningful length
    if total_chars > 0 and alpha_chars / total_chars >= 0.4 and alpha_chars >= 5:
        return True

    # Special case: keep product codes like "KOB_1 PCS_Monster_CAR"
    if re.search(r'[A-Z][A-Z0-9_]+', text) and alpha_chars >= 5:
        return True

    # Keep if it has meaningful words (at least 3 letters in a row)
    if re.search(r'[a-zA-Z]{3,}', text) and alpha_chars >= 5:
        return True

    return False

def remove_cess_content(text):
    """Remove CESS keyword and its associated value"""
    # Pattern to match CESS with its value
    cess_patterns = [
        r'\bCESS\b\s*:\s*[\d\.]+',  # CESS: 0.00
        r'\bCESS\b\s+[\d\.]+',      # CESS 0.00
        r'\bCESS\b\s*:\s*\S+',     # CESS: value
        r'\bCESS\b'                 # Just CESS
    ]

    for pattern in cess_patterns:
        matches = list(re.finditer(pattern, text, re.IGNORECASE))
        for match in reversed(matches):
            removed_text = match.group()
            print(f"   🗑️ Removing CESS: '{removed_text}'")
            text = text[:match.start()] + ' ' + text[match.end():]
            break  # Remove only first match per pattern

    return text

def remove_shipping_charges(text):
    """Remove Shipping and Handling Charges content"""
    # Patterns to match shipping charges
    shipping_patterns = [
        r'Shipping\s+and\s+Handling\s+[\d\.\s]*Charges?',
        r'Shipping\s+and\s+Handling\s+Charges?[\d\.\s]*',
        r'Shipping\s*&\s*Handling\s+[\d\.\s]*Charges?',
        r'Shipping\s+Charges?[\d\.\s]*',
        r'Handling\s+Charges?[\d\.\s]*'
    ]

    for pattern in shipping_patterns:
        matches = list(re.finditer(pattern, text, re.IGNORECASE))
        for match in reversed(matches):
            removed_text = match.group()
            print(f"   🗑️ Removing shipping: '{removed_text}'")
            text = text[:match.start()] + ' ' + text[match.end():]

    return text

def combine_multiline_values(values, field_name):
    """Combine multiple values that might represent a single multi-line item"""
    if not values or len(values) <= 1:
        return values

    # For product-related fields, try to combine consecutive text values
    if field_name in ["Product", "Description", "Title"]:
        combined_items = []
        current_item = []

        for value in values:
            if value and isinstance(value, str):
                # Apply trimming to each value before processing
                trimmed_value = trim_at_keywords(value, field_name)

                # Skip if trimming resulted in empty or very short text
                if not trimmed_value or len(trimmed_value.strip()) < 3:
                    continue

                # Check if this looks like a continuation of the previous item
                if (current_item and
                    len(trimmed_value) > 10 and
                    not re.match(r'^\d+', trimmed_value) and  # Doesn't start with number
                    not any(keyword in trimmed_value.lower() for keyword in ['hsn', 'sac', 'qty', 'total', 'igst', 'imei'])):
                    current_item.append(trimmed_value)
                else:
                    # Start a new item
                    if current_item:
                        combined_text = ' '.join(current_item)
                        # Apply final trimming to the combined text
                        final_trimmed = trim_at_keywords(combined_text, field_name)
                        if final_trimmed and len(final_trimmed.strip()) > 3:
                            combined_items.append(final_trimmed)
                    current_item = [trimmed_value]

        # Add the last item
        if current_item:
            combined_text = ' '.join(current_item)
            # Apply final trimming to the combined text
            final_trimmed = trim_at_keywords(combined_text, field_name)
            if final_trimmed and len(final_trimmed.strip()) > 3:
                combined_items.append(final_trimmed)

        return combined_items if combined_items else values

    return values

def calculate_table_score(table_data):
    """Calculate a score for how much relevant data a table contains"""
    score = 0

    # Score based on number of fields with data
    for field_name, field_data in table_data.items():
        if field_data:  # If field has data
            score += 10

            # Bonus points for important fields
            if field_name in ["Product", "Description", "Title"]:
                score += 20
            elif field_name in ["Gross Amount", "Total", "Taxable Value"]:
                score += 15
            elif field_name in ["Qty", "IGST"]:
                score += 10

            # Bonus for multiple items
            if len(field_data) > 1:
                score += 5

    return score

def process_pdf_file(pdf_path):
    """Process a single PDF file and extract table data from all pages"""
    print(f"\n🔍 Processing PDF: {os.path.basename(pdf_path)}")

    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            total_pages = len(pdf_reader.pages)
            print(f"   📄 Total pages: {total_pages}")

            all_extracted_data = []

            for page_num in range(1, total_pages + 1):
                print(f"\n   📄 Processing page {page_num}/{total_pages}...")

                # Extract table data from this page
                page_data = extract_table_data_with_tabula(pdf_path, page_num)

                if page_data:
                    page_result = {
                        "page": page_num,
                        "pdf_file": os.path.basename(pdf_path),
                        "extracted_data": page_data
                    }
                    all_extracted_data.append(page_result)

                    # Display summary of extracted data
                    print(f"   ✅ Page {page_num} data extracted:")
                    for field, values in page_data.items():
                        if values:
                            print(f"      {field}: {len(values)} item(s) - {values[:2]}{'...' if len(values) > 2 else ''}")
                else:
                    print(f"   ⚠️ No table data found on page {page_num}")

            return all_extracted_data

    except Exception as e:
        print(f"   ❌ Error processing PDF {pdf_path}: {str(e)}")
        return []

def process_folder(folder_path):
    """Process all PDF files in a folder"""
    print(f"\n📁 Processing folder: {folder_path}")

    if not os.path.exists(folder_path):
        print(f"❌ Folder does not exist: {folder_path}")
        return {}

    # Find all PDF files
    pdf_files = []
    for file in os.listdir(folder_path):
        if file.lower().endswith('.pdf'):
            pdf_files.append(os.path.join(folder_path, file))

    if not pdf_files:
        print(f"❌ No PDF files found in folder: {folder_path}")
        return {}

    print(f"📄 Found {len(pdf_files)} PDF file(s)")

    # Process each PDF file
    all_results = {}
    for pdf_path in pdf_files:
        pdf_name = os.path.basename(pdf_path).replace('.pdf', '')
        pdf_data = process_pdf_file(pdf_path)

        if pdf_data:
            all_results[pdf_name] = pdf_data
            print(f"✅ Successfully processed: {pdf_name}")
        else:
            print(f"⚠️ No data extracted from: {pdf_name}")

    return all_results

def save_results(results, output_file=None):
    """Save extraction results to JSON file"""
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"table_extraction_results_{timestamp}.json"

    try:
        # Prepare final results structure
        final_results = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "total_pdfs_processed": len(results),
            "extraction_summary": {},
            "detailed_results": results
        }

        # Create summary
        total_pages = 0
        total_tables = 0
        for pdf_name, pdf_data in results.items():
            pages_with_data = len(pdf_data)
            total_pages += pages_with_data
            total_tables += pages_with_data

            final_results["extraction_summary"][pdf_name] = {
                "pages_processed": pages_with_data,
                "tables_found": pages_with_data
            }

        final_results["total_pages_processed"] = total_pages
        final_results["total_tables_extracted"] = total_tables

        # Save to file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=4, ensure_ascii=False)

        print(f"\n💾 Results saved to: {output_file}")
        print(f"📊 Summary:")
        print(f"   📄 PDFs processed: {len(results)}")
        print(f"   📋 Pages processed: {total_pages}")
        print(f"   📊 Tables extracted: {total_tables}")

        return output_file

    except Exception as e:
        print(f"❌ Error saving results: {str(e)}")
        return None

def display_results_summary(results):
    """Display a summary of extraction results"""
    print("\n" + "="*80)
    print("📊 EXTRACTION RESULTS SUMMARY")
    print("="*80)

    if not results:
        print("❌ No results to display")
        return

    for pdf_name, pdf_data in results.items():
        print(f"\n📄 PDF: {pdf_name}")
        print(f"   📋 Pages with data: {len(pdf_data)}")

        for page_result in pdf_data:
            page_num = page_result["page"]
            extracted_data = page_result["extracted_data"]

            print(f"\n   📄 Page {page_num}:")
            for field, values in extracted_data.items():
                if values:
                    if isinstance(values, list) and len(values) > 0:
                        if isinstance(values[0], (int, float)):
                            print(f"      {field}: {values}")
                        else:
                            # For text fields, show first few characters
                            display_values = []
                            for val in values[:3]:  # Show max 3 items
                                if isinstance(val, str) and len(val) > 50:
                                    display_values.append(val[:47] + "...")
                                else:
                                    display_values.append(str(val))
                            more_text = f" (+{len(values)-3} more)" if len(values) > 3 else ""
                            print(f"      {field}: {display_values}{more_text}")

def convert_results_to_dataframe(results):
    """
    Convert extracted nested results into a flat pandas DataFrame
    """
    records = []

    for pdf_name, pdf_data in results.items():
        for page_data in pdf_data:
            page = page_data.get("page")
            pdf_file = page_data.get("pdf_file")
            data = page_data.get("extracted_data", {})

            # Find max number of products or fields
            max_len = max(len(v) for v in data.values() if isinstance(v, list)) if data else 1

            for i in range(max_len):
                record = {
                    "PDF File": pdf_file,
                    "Page": page
                }

                for field, values in data.items():
                    if isinstance(values, list):
                        record[field] = values[i] if i < len(values) else None
                    else:
                        record[field] = values

                records.append(record)

    df = pd.DataFrame(records)
    return df


# Main execution function
def main(folder_path=None):
    """Main function to run the table extraction"""
    print("="*80)
    print("🎯 PDF TABLE DATA EXTRACTOR USING TABULA-PY")
    print("="*80)
    print("Extracting structured data from unstructured PDF tables...")

    # Use default folder if none provided
    if not folder_path:
        folder_path = "Invoice"  # Default folder name

    # Process the folder
    results = process_folder(folder_path)

    if results:
        # Display summary
        display_results_summary(results)

        # Save results
        output_file = save_results(results)

        df = convert_results_to_dataframe(results)
        print("\n🧾 DataFrame Preview:")
        print(df.head())

        # Optional: Save to CSV
        df.to_csv("extracted_invoice_table.csv", index=False)
        print("📁 DataFrame saved to 'extracted_invoice_table.csv'")


        print(f"\n🎉 Extraction completed successfully!")
        if output_file:
            print(f"📁 Results saved to: {output_file}")

        return results
    else:
        print("\n❌ No data extracted from any PDF files")
        return None

# Main execution
if __name__ == "__main__":
    import sys

    # Get folder path from command line argument or use default
    folder_path = sys.argv[1] if len(sys.argv) > 1 else None

    # Run the extraction
    results = main(folder_path)

    if results:
        print(f"\n✅ Processing completed successfully!")
    else:
        print("\n❌ Processing failed!")
        sys.exit(1)